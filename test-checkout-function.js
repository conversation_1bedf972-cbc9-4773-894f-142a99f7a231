// Test the createCheckoutSession Firebase Function
console.log('🧪 Testing createCheckoutSession function...');

const FUNCTION_URL = 'https://us-central1-h1c1-798a8.cloudfunctions.net/createCheckoutSession';

async function testCheckoutFunction() {
    try {
        console.log('📡 Testing createCheckoutSession endpoint...');
        
        // Test with GET request (should return 405 or error)
        const getResponse = await fetch(FUNCTION_URL, {
            method: 'GET'
        });
        
        console.log(`GET Response Status: ${getResponse.status}`);
        console.log(`GET Response Text: ${await getResponse.text()}`);
        
        // Test with POST request (should require authentication)
        const postResponse = await fetch(FUNCTION_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                data: {
                    listingId: 'test-listing-123',
                    quantity: 1
                }
            })
        });
        
        console.log(`POST Response Status: ${postResponse.status}`);
        console.log(`POST Response Text: ${await postResponse.text()}`);
        
        if (postResponse.status === 401) {
            console.log('✅ Function correctly requires authentication');
        } else {
            console.log('⚠️ Unexpected response from function');
        }
        
    } catch (error) {
        console.error('❌ Error testing function:', error.message);
    }
}

testCheckoutFunction();
