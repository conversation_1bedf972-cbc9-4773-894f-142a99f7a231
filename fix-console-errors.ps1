# Fix Console Errors - Deploy Script
# This script fixes the getSellerPendingPayouts 500 errors

Write-Host "🔧 Fixing Console Errors in Student Dashboard..." -ForegroundColor Cyan

# Step 1: Deploy Firestore indexes first
Write-Host "`n📊 Step 1: Deploying Firestore indexes..." -ForegroundColor Yellow
Write-Host "This will create the required composite index for orders collection" -ForegroundColor Gray

try {
    firebase deploy --only firestore:indexes
    Write-Host "✅ Firestore indexes deployed successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to deploy Firestore indexes: $_" -ForegroundColor Red
    Write-Host "Please run manually: firebase deploy --only firestore:indexes" -ForegroundColor Yellow
}

# Step 2: Wait for indexes to build
Write-Host "`n⏳ Step 2: Waiting for indexes to build..." -ForegroundColor Yellow
Write-Host "This may take a few minutes. You can check progress at:" -ForegroundColor Gray
Write-Host "https://console.firebase.google.com/project/h1c1-798a8/firestore/indexes" -ForegroundColor Blue

# Step 3: Deploy updated cloud functions
Write-Host "`n🚀 Step 3: Deploying updated cloud functions..." -ForegroundColor Yellow
Write-Host "This includes improved error handling and fallback queries" -ForegroundColor Gray

try {
    firebase deploy --only functions:getSellerPendingPayouts
    Write-Host "✅ Cloud function deployed successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to deploy cloud function: $_" -ForegroundColor Red
    Write-Host "Please run manually: firebase deploy --only functions" -ForegroundColor Yellow
}

# Step 4: Test the fix
Write-Host "`n🧪 Step 4: Testing the fix..." -ForegroundColor Yellow
Write-Host "Please test the following in your browser:" -ForegroundColor Gray
Write-Host "1. Open the student dashboard" -ForegroundColor White
Write-Host "2. Check the browser console for errors" -ForegroundColor White
Write-Host "3. Try accessing seller features (if applicable)" -ForegroundColor White

Write-Host "`n📋 Summary of fixes applied:" -ForegroundColor Cyan
Write-Host "✅ Added composite Firestore index for orders collection" -ForegroundColor Green
Write-Host "✅ Improved error handling in getSellerPendingPayouts function" -ForegroundColor Green
Write-Host "✅ Added fallback query mechanism" -ForegroundColor Green
Write-Host "✅ Enhanced frontend error messages" -ForegroundColor Green
Write-Host "✅ Added handling for ERR_BLOCKED_BY_CLIENT errors" -ForegroundColor Green

Write-Host "`n🔍 If errors persist:" -ForegroundColor Yellow
Write-Host "1. Check if ad blockers are blocking Firestore connections" -ForegroundColor White
Write-Host "2. Verify indexes are built: firebase firestore:indexes" -ForegroundColor White
Write-Host "3. Check function logs: firebase functions:log" -ForegroundColor White
Write-Host "4. Contact support if issues continue" -ForegroundColor White

Write-Host "`n🎉 Fix deployment complete!" -ForegroundColor Green
