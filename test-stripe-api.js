// Test the current stripeApi function
console.log('🧪 Testing current stripeApi function...');

async function testStripeApi() {
    try {
        console.log('📡 Testing stripeApi/create-checkout-session endpoint...');
        
        const url = 'https://us-central1-h1c1-798a8.cloudfunctions.net/stripeApi/create-checkout-session';
        
        // Test with GET request first
        console.log('Testing GET request...');
        const getResponse = await fetch(url, {
            method: 'GET'
        });
        
        console.log(`GET Response Status: ${getResponse.status}`);
        console.log(`GET Response Text: ${await getResponse.text()}`);
        
        // Test with POST request (no auth - should fail)
        console.log('\nTesting POST request without auth...');
        const postResponse = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                listingId: 'test-listing-123',
                quantity: 1
            })
        });
        
        console.log(`POST Response Status: ${postResponse.status}`);
        const postText = await postResponse.text();
        console.log(`POST Response Text: ${postText}`);
        
        if (postResponse.status === 401) {
            console.log('✅ Function correctly requires authentication');
        } else if (postResponse.status === 500) {
            console.log('❌ Function has internal error - needs fixing');
        } else {
            console.log('⚠️ Unexpected response');
        }
        
    } catch (error) {
        console.error('❌ Error testing stripeApi:', error.message);
    }
}

testStripeApi();
