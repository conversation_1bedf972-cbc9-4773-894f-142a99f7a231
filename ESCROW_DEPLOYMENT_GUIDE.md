# Escrow System Deployment Guide

## 🚀 Overview
This guide covers the deployment of the new commission-based escrow system with secret code release, automatic fund disbursement, and integrated Shippo shipping for Hive Campus.

## 📋 Pre-Deployment Checklist

### 1. Environment Configuration
```bash
# Firebase Functions Configuration
firebase functions:config:set shippo.api_key="YOUR_SHIPPO_API_KEY"
firebase functions:config:set stripe.secret_key="YOUR_STRIPE_SECRET_KEY"
firebase functions:config:set stripe.webhook_secret="YOUR_STRIPE_WEBHOOK_SECRET"
```

### 2. Required Dependencies
Ensure these packages are installed in `functions/package.json`:
```json
{
  "dependencies": {
    "firebase-functions": "^4.0.0",
    "firebase-admin": "^11.0.0",
    "stripe": "^12.0.0",
    "express": "^4.18.0"
  }
}
```

### 3. Firestore Security Rules
Update Firestore rules to include new collections:
```javascript
// Add to firestore.rules
match /shippingLabels/{orderId} {
  allow read, write: if request.auth != null && 
    (resource.data.sellerId == request.auth.uid || 
     resource.data.buyerId == request.auth.uid);
}

match /orders/{orderId} {
  allow read, write: if request.auth != null && 
    (resource.data.sellerId == request.auth.uid || 
     resource.data.buyerId == request.auth.uid);
}
```

## 🔧 Deployment Steps

### 1. Deploy Firebase Functions
```bash
cd functions
npm run build
firebase deploy --only functions
```

### 2. Deploy Frontend
```bash
npm run build
firebase deploy --only hosting
```

### 3. Configure Webhooks
Set up Shippo webhooks to point to your Firebase Functions:
- Webhook URL: `https://your-region-your-project.cloudfunctions.net/trackingWebhook`
- Events: `track_updated`, `track_delivered`

### 4. Test Deployment
Run the test scenarios from `test-commission-structure.md`

## 🔄 Migration Strategy

### Phase 1: Backward Compatibility
- New commission structure applies to new orders only
- Existing orders continue with old flow
- Gradual rollout to test users

### Phase 2: Full Migration
- All new orders use escrow system
- Update existing order statuses
- Enable auto-release scheduler

### Phase 3: Cleanup
- Remove old commission calculation code
- Archive legacy order handling
- Update documentation

## 📊 Monitoring & Analytics

### Key Metrics to Track
1. **Commission Revenue**
   - Track platform fees by category
   - Monitor flat fee vs percentage fee distribution
   - Calculate total revenue impact

2. **Escrow Performance**
   - Secret code redemption rate
   - Auto-release frequency
   - Return request volume

3. **Shipping Integration**
   - Label generation success rate
   - Delivery confirmation accuracy
   - Tracking update frequency

### Logging Points
```typescript
// Key events to log
- Order creation with new commission structure
- Secret code generation and redemption
- Shipping label creation
- Delivery confirmations
- Fund releases (manual and automatic)
- Return requests
```

## 🚨 Rollback Plan

### If Issues Arise:
1. **Disable New Functions**
   ```bash
   firebase functions:config:unset escrow.enabled
   firebase deploy --only functions
   ```

2. **Revert to Old Commission Structure**
   - Comment out new commission logic
   - Restore old percentage-based calculation
   - Redeploy functions

3. **Data Cleanup**
   - Mark problematic orders for manual review
   - Process any stuck escrow funds
   - Notify affected users

## 🔐 Security Considerations

### Secret Code Security
- Codes are cryptographically random
- Single-use enforcement
- Expiration after 30 days
- Rate limiting on attempts

### Fund Protection
- Escrow holds funds securely
- Multiple verification layers
- Audit trail for all transactions
- Admin override capabilities

### API Security
- Authentication required for all endpoints
- User permission validation
- Input sanitization
- Rate limiting

## 📞 Support Procedures

### Common Issues & Solutions

1. **Secret Code Not Working**
   - Verify order status
   - Check code expiration
   - Regenerate if necessary

2. **Shipping Label Failures**
   - Validate address format
   - Check Shippo API status
   - Retry with different carrier

3. **Auto-Release Not Triggering**
   - Check Cloud Scheduler status
   - Verify order eligibility
   - Manual release if needed

### Escalation Process
1. Level 1: Automated error handling
2. Level 2: Admin dashboard intervention
3. Level 3: Manual database updates
4. Level 4: Developer investigation

## 📈 Success Metrics

### Week 1 Targets
- 95% successful order creation
- 90% secret code redemption rate
- 85% shipping label generation success

### Month 1 Targets
- 98% system uptime
- <1% manual intervention rate
- 92% user satisfaction score

### Quarter 1 Targets
- 15% increase in platform revenue
- 25% reduction in support tickets
- 95% automated fund release rate

## 🔄 Maintenance Schedule

### Daily
- Monitor error logs
- Check escrow balances
- Review failed transactions

### Weekly
- Analyze commission trends
- Update shipping rates
- Process return requests

### Monthly
- Security audit
- Performance optimization
- User feedback review

## 📚 Documentation Updates

### User-Facing
- Update help articles
- Create video tutorials
- Add FAQ entries

### Developer
- API documentation
- Code comments
- Architecture diagrams

This deployment ensures a smooth transition to the new escrow system while maintaining platform stability and user trust.
