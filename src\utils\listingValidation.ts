import { Listing } from '../firebase/types';

/**
 * Validates a listing object to ensure it has all required fields for checkout
 */
export function validateListingForCheckout(listing: Listing | null): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!listing) {
    errors.push('Listing data is missing');
    return { isValid: false, errors, warnings };
  }

  // Check for required basic fields
  if (!listing.id) {
    errors.push('Listing ID is missing');
  }

  if (!listing.title) {
    errors.push('Listing title is missing');
  }

  if (!listing.price || listing.price <= 0) {
    errors.push('Listing price is invalid');
  }

  // Check for seller information - this is critical for checkout
  if (!listing.ownerId && !listing.userId) {
    errors.push('Seller information is missing - this listing cannot be purchased');
  } else if (!listing.ownerId && listing.userId) {
    warnings.push('Listing uses legacy userId field instead of ownerId');
  }

  // Check listing status
  if (listing.status !== 'active') {
    errors.push(`Listing is not active (status: ${listing.status})`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Gets the seller ID from a listing, handling both ownerId and legacy userId fields
 */
export function getSellerIdFromListing(listing: Listing | null): string | null {
  if (!listing) {
    return null;
  }

  return listing.ownerId || listing.userId || null;
}

/**
 * Formats validation errors for display to the user
 */
export function formatValidationErrors(errors: string[]): string {
  if (errors.length === 0) {
    return '';
  }

  if (errors.length === 1) {
    return errors[0];
  }

  return `Multiple issues found:\n${errors.map(error => `• ${error}`).join('\n')}`;
}

/**
 * Checks if a listing can be purchased by the current user
 */
export function canPurchaseListing(listing: Listing | null, currentUserId: string | null): {
  canPurchase: boolean;
  reason?: string;
} {
  const validation = validateListingForCheckout(listing);
  
  if (!validation.isValid) {
    return {
      canPurchase: false,
      reason: formatValidationErrors(validation.errors)
    };
  }

  if (!currentUserId) {
    return {
      canPurchase: false,
      reason: 'You must be logged in to purchase items'
    };
  }

  const sellerId = getSellerIdFromListing(listing);
  if (sellerId === currentUserId) {
    return {
      canPurchase: false,
      reason: 'You cannot purchase your own listing'
    };
  }

  return { canPurchase: true };
}
