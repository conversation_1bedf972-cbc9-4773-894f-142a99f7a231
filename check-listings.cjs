const admin = require('firebase-admin');

// Initialize Firebase Admin
const serviceAccount = require('./functions/src/config/h1c1-798a8-firebase-adminsdk-ixqhj-b8b8b8b8b8.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  databaseURL: 'https://h1c1-798a8-default-rtdb.firebaseio.com'
});

const db = admin.firestore();

async function checkListings() {
  try {
    console.log('🔍 Checking all listings for ownerId field...');
    
    const listingsSnapshot = await db.collection('listings').get();
    
    if (listingsSnapshot.empty) {
      console.log('❌ No listings found in database');
      return;
    }
    
    console.log(`📚 Found ${listingsSnapshot.size} listings`);
    
    let missingOwnerIdCount = 0;
    let validListingsCount = 0;
    
    listingsSnapshot.forEach(doc => {
      const listing = doc.data();
      const listingId = doc.id;
      
      console.log(`\n📖 Listing ID: ${listingId}`);
      console.log(`   Title: ${listing.title}`);
      console.log(`   ownerId: ${listing.ownerId}`);
      console.log(`   userId: ${listing.userId}`);
      console.log(`   Has ownerId: ${!!listing.ownerId}`);
      console.log(`   Has userId: ${!!listing.userId}`);
      
      if (!listing.ownerId && !listing.userId) {
        console.log(`   ❌ MISSING BOTH ownerId AND userId!`);
        missingOwnerIdCount++;
      } else {
        console.log(`   ✅ Has seller ID`);
        validListingsCount++;
      }
    });
    
    console.log(`\n📊 Summary:`);
    console.log(`   Total listings: ${listingsSnapshot.size}`);
    console.log(`   Valid listings: ${validListingsCount}`);
    console.log(`   Missing seller ID: ${missingOwnerIdCount}`);
    
    if (missingOwnerIdCount > 0) {
      console.log(`\n⚠️  Found ${missingOwnerIdCount} listings without seller ID!`);
      console.log('   These listings will cause checkout errors.');
    } else {
      console.log(`\n✅ All listings have seller IDs`);
    }
    
  } catch (error) {
    console.error('❌ Error checking listings:', error);
  }
}

checkListings().then(() => {
  console.log('✅ Check complete');
  process.exit(0);
}).catch(error => {
  console.error('❌ Script failed:', error);
  process.exit(1);
});
