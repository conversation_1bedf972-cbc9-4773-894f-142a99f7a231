import React, { useState, useEffect } from 'react';
import { Package, Clock, CheckCircle, XCircle, Eye, Calendar, DollarSign, User, ArrowLeft, Filter, Truck, MapPin, Lock, AlertCircle, Copy, FileText, Shield } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
// import { useAuth } from '../hooks/useAuth'; // Temporarily removed to fix hook ordering
import { formatPrice } from '../utils/priceUtils';
import EscrowOrderCard from '../components/EscrowOrderCard';

// Use a flexible type for orders that can handle both Firebase and mock orders
type Order = any;

const OrderHistory: React.FC = () => {
  const navigate = useNavigate();

  // All state hooks declared at the top - NEVER conditional
  const [activeTab, setActiveTab] = useState<'all' | 'active' | 'completed'>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [secretCode, setSecretCode] = useState<{ [orderId: string]: string }>({});
  const [submittingCode, setSubmittingCode] = useState<string | null>(null);
  const [codeErrors, setCodeErrors] = useState<{ [orderId: string]: string }>({});
  const [expandedOrder, setExpandedOrder] = useState<string | null>(null);

  // Single useEffect to load orders - NEVER conditional
  useEffect(() => {
    const loadOrders = () => {
      setIsLoading(true);

      // Load real orders from Firebase - no mock data
      setIsLoading(false);
    };

    loadOrders();
  }, []); // Empty dependency array - only run once

  // Handle delivery code submission
  const handleSubmitDeliveryCode = async (orderId: string) => {
    const code = secretCode[orderId];
    if (!code || code.length !== 6) {
      setCodeErrors({ ...codeErrors, [orderId]: 'Please enter a valid 6-digit code' });
      return;
    }

    setSubmittingCode(orderId);
    setCodeErrors({ ...codeErrors, [orderId]: '' });

    try {
      // Check if this is a mock order
      if (orderId.startsWith('order_test_')) {
        const mockOrderData = localStorage.getItem(`mock_order_${orderId}`);
        if (mockOrderData) {
          const mockOrder = JSON.parse(mockOrderData);

          if (code === mockOrder.deliveryCode || code === mockOrder.secretCode) {
            // Update mock order status
            const updatedOrder = {
              ...mockOrder,
              status: 'completed',
              completedAt: new Date().toISOString()
            };

            localStorage.setItem(`mock_order_${orderId}`, JSON.stringify(updatedOrder));

            // Update local state
            setOrders(prevOrders =>
              prevOrders.map(order =>
                order.id === orderId
                  ? { ...order, status: 'completed' as const }
                  : order
              )
            );

            alert('🎉 Mock delivery confirmed! Order marked as completed.');
            return;
          } else {
            setCodeErrors({ ...codeErrors, [orderId]: 'Invalid delivery code. Please check and try again.' });
            return;
          }
        }
      }

      // For real orders, use the actual API
      const { httpsCallable } = await import('firebase/functions');
      const { functions } = await import('../firebase/config');

      const redeemSecretCode = httpsCallable(functions, 'redeemSecretCode');
      const result = await redeemSecretCode({ orderId, secretCode: code });

      if (result.data && (result.data as any).success) {
        // Update local state
        setOrders(prevOrders =>
          prevOrders.map(order =>
            order.id === orderId
              ? { ...order, status: 'completed' as const }
              : order
          )
        );

        // Clear the secret code input
        setSecretCode({ ...secretCode, [orderId]: '' });

        alert('🎉 Delivery confirmed! Funds have been released to the seller.');
      } else {
        setCodeErrors({
          ...codeErrors,
          [orderId]: 'Invalid secret code. Please check and try again.'
        });
      }
    } catch (error) {
      console.error('Error submitting delivery code:', error);
      setCodeErrors({
        ...codeErrors,
        [orderId]: error instanceof Error ? error.message : 'Failed to verify code'
      });
    } finally {
      setSubmittingCode(null);
    }
  };

  // Copy delivery code to clipboard
  const copyDeliveryCode = (code: string) => {
    navigator.clipboard.writeText(code);
    alert('Delivery code copied to clipboard!');
  };



  // Refresh orders manually
  const refreshOrders = () => {
    window.location.reload();
  };

  // Individual Order PDF Download
  const downloadOrderPDF = (order: Order) => {
    const printContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Order Receipt - ${order.id} - Hive Campus</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; color: #333; line-height: 1.6; }
          .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #007bff; padding-bottom: 20px; }
          .logo { font-size: 28px; font-weight: bold; color: #007bff; margin-bottom: 10px; }
          .receipt-title { font-size: 24px; color: #333; margin-bottom: 5px; }
          .order-info { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
          .order-details { display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 20px; }
          .detail-section { }
          .detail-title { font-size: 18px; font-weight: bold; margin-bottom: 15px; color: #007bff; border-bottom: 1px solid #ddd; padding-bottom: 5px; }
          .detail-item { margin-bottom: 8px; display: flex; justify-content: space-between; }
          .detail-label { font-weight: bold; }
          .price-summary { background-color: #e9ecef; padding: 20px; border-radius: 8px; margin: 20px 0; }
          .price-item { display: flex; justify-content: space-between; margin-bottom: 10px; }
          .total-price { font-size: 20px; font-weight: bold; color: #007bff; border-top: 2px solid #007bff; padding-top: 10px; margin-top: 10px; }
          .status-badge { display: inline-block; padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: bold; margin: 10px 0; }
          .status-completed { background-color: #d4edda; color: #155724; }
          .status-shipped { background-color: #d1ecf1; color: #0c5460; }
          .status-payment { background-color: #fff3cd; color: #856404; }
          .delivery-info { background-color: #d4edda; padding: 15px; border-radius: 8px; margin: 20px 0; }
          .tracking-info { background-color: #cce5ff; padding: 15px; border-radius: 8px; margin: 20px 0; }
          .footer { margin-top: 40px; text-align: center; color: #666; font-size: 12px; border-top: 1px solid #ddd; padding-top: 20px; }
          .qr-placeholder { width: 100px; height: 100px; background-color: #f0f0f0; border: 2px dashed #ccc; display: flex; align-items: center; justify-content: center; margin: 20px auto; }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="logo">🐝 Hive Campus</div>
          <div class="receipt-title">Order Receipt</div>
          <p>Official order confirmation and receipt</p>
        </div>

        <div class="order-info">
          <h2>${order.title}</h2>
          <div class="status-badge status-${order.status.replace('_', '-')}">
            ${getStatusText(order.status)}
          </div>
          <p><strong>Order ID:</strong> ${order.id}</p>
          <p><strong>Order Date:</strong> ${new Date(order.createdAt).toLocaleDateString()} at ${new Date(order.createdAt).toLocaleTimeString()}</p>
          <p><strong>Seller:</strong> ${order.sellerName}</p>
        </div>

        <div class="order-details">
          <div class="detail-section">
            <div class="detail-title">📦 Item Details</div>
            <div class="detail-item">
              <span class="detail-label">Product:</span>
              <span>${order.title}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Listing ID:</span>
              <span>${order.listingId}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Seller:</span>
              <span>${order.sellerName}</span>
            </div>
            ${order.status === 'completed' && order.completedAt ? `
              <div class="detail-item">
                <span class="detail-label">Completed:</span>
                <span>${new Date(order.completedAt).toLocaleDateString()}</span>
              </div>
            ` : ''}
          </div>

          <div class="detail-section">
            <div class="detail-title">💳 Payment Information</div>
            <div class="price-summary">
              <div class="price-item">
                <span>Item Price:</span>
                <span>$${formatPrice(order.amount || order.price || 0)}</span>
              </div>
              ${order.shippingFee && order.shippingFee > 0 ? `
                <div class="price-item">
                  <span>Shipping & Handling:</span>
                  <span>$${formatPrice(order.shippingFee)}</span>
                </div>
              ` : ''}
              <div class="price-item total-price">
                <span>Total Paid:</span>
                <span>$${formatPrice(order.total || order.amount || order.price || 0)}</span>
              </div>
            </div>
            <p><strong>Payment Status:</strong> ✅ Completed</p>
          </div>
        </div>

        ${order.shippingAddress ? `
          <div class="detail-section">
            <div class="detail-title">📍 Shipping Address</div>
            <p>${order.shippingAddress.firstName} ${order.shippingAddress.lastName}</p>
            <p>${order.shippingAddress.addressLine1}</p>
            ${order.shippingAddress.addressLine2 ? `<p>${order.shippingAddress.addressLine2}</p>` : ''}
            <p>${order.shippingAddress.city}, ${order.shippingAddress.state} ${order.shippingAddress.zipCode}</p>
            <p>${order.shippingAddress.country || 'United States'}</p>
          </div>
        ` : ''}

        ${order.trackingInfo?.trackingNumber ? `
          <div class="tracking-info">
            <div class="detail-title">🚚 Shipping & Tracking</div>
            <p><strong>Carrier:</strong> ${order.trackingInfo.carrier || 'Standard Shipping'}</p>
            <p><strong>Tracking Number:</strong> ${order.trackingInfo.trackingNumber}</p>
            ${order.trackingInfo.estimatedDeliveryDate ? `
              <p><strong>Estimated Delivery:</strong> ${new Date(order.trackingInfo.estimatedDeliveryDate).toLocaleDateString()}</p>
            ` : ''}
          </div>
        ` : ''}

        ${order.status === 'completed' ? `
          <div class="delivery-info">
            <div class="detail-title">✅ Delivery Confirmed</div>
            <p><strong>Status:</strong> Order completed and delivered</p>
            <p><strong>Confirmation:</strong> Delivery verified by buyer</p>
            ${order.completedAt ? `<p><strong>Confirmed On:</strong> ${new Date(order.completedAt).toLocaleDateString()}</p>` : ''}
          </div>
        ` : ''}

        ${order.status === 'shipped_pending_code' ? `
          <div class="delivery-info">
            <div class="detail-title">🔐 Delivery Confirmation Pending</div>
            <p><strong>Status:</strong> Package shipped, awaiting delivery confirmation</p>
            <p><strong>Action Required:</strong> Enter delivery code when package arrives</p>
            <p><strong>Delivery Code:</strong> Will be provided upon delivery</p>
          </div>
        ` : ''}

        <div class="qr-placeholder">
          <span style="color: #999;">QR Code</span>
        </div>

        <div class="footer">
          <p><strong>Hive Campus</strong> - University Marketplace</p>
          <p>This is an official receipt for your order. Keep this for your records.</p>
          <p>For support, contact: <EMAIL></p>
          <p>Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}</p>
        </div>
      </body>
      </html>
    `;

    // Create a new window for printing
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(printContent);
      printWindow.document.close();

      // Wait for content to load then print
      printWindow.onload = () => {
        printWindow.print();
        // Close the window after printing (optional)
        setTimeout(() => {
          printWindow.close();
        }, 1000);
      };
    }
  };

  // PDF Download functionality for all orders
  const downloadOrderHistoryPDF = () => {
    const printContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Order History - Hive Campus</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; color: #333; }
          .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #007bff; padding-bottom: 20px; }
          .logo { font-size: 24px; font-weight: bold; color: #007bff; }
          .order { margin-bottom: 30px; border: 1px solid #ddd; padding: 20px; border-radius: 8px; }
          .order-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
          .order-title { font-size: 18px; font-weight: bold; }
          .order-status { padding: 5px 10px; border-radius: 15px; font-size: 12px; font-weight: bold; }
          .status-completed { background-color: #d4edda; color: #155724; }
          .status-shipped { background-color: #d1ecf1; color: #0c5460; }
          .status-payment { background-color: #fff3cd; color: #856404; }
          .order-details { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
          .detail-section { }
          .detail-title { font-weight: bold; margin-bottom: 10px; color: #007bff; }
          .detail-item { margin-bottom: 5px; }
          .price-summary { background-color: #f8f9fa; padding: 15px; border-radius: 5px; }
          .total-price { font-size: 18px; font-weight: bold; color: #007bff; }
          .footer { margin-top: 40px; text-align: center; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="logo">🐝 Hive Campus</div>
          <h1>Order History Report</h1>
          <p>Generated on ${new Date().toLocaleDateString()}</p>
        </div>

        ${orders.map(order => `
          <div class="order">
            <div class="order-header">
              <div class="order-title">${order.title}</div>
              <div class="order-status status-${order.status.replace('_', '-')}">
                ${getStatusText(order.status)}
              </div>
            </div>

            <div class="order-details">
              <div class="detail-section">
                <div class="detail-title">Order Information</div>
                <div class="detail-item"><strong>Order ID:</strong> ${order.id}</div>
                <div class="detail-item"><strong>Date:</strong> ${new Date(order.createdAt).toLocaleDateString()}</div>
                <div class="detail-item"><strong>Seller:</strong> ${order.sellerName}</div>
                ${order.trackingInfo?.trackingNumber ? `<div class="detail-item"><strong>Tracking:</strong> ${order.trackingInfo.trackingNumber}</div>` : ''}
              </div>

              <div class="detail-section">
                <div class="detail-title">Payment Summary</div>
                <div class="price-summary">
                  <div class="detail-item">Item Price: $${formatPrice(order.amount)}</div>
                  ${order.shippingFee ? `<div class="detail-item">Shipping: $${formatPrice(order.shippingFee)}</div>` : ''}
                  <div class="detail-item total-price">Total: $${formatPrice(order.total || order.amount)}</div>
                </div>
              </div>
            </div>

            ${order.shippingAddress ? `
              <div style="margin-top: 15px;">
                <div class="detail-title">Shipping Address</div>
                <div class="detail-item">${order.shippingAddress.firstName} ${order.shippingAddress.lastName}</div>
                <div class="detail-item">${order.shippingAddress.addressLine1}</div>
                ${order.shippingAddress.addressLine2 ? `<div class="detail-item">${order.shippingAddress.addressLine2}</div>` : ''}
                <div class="detail-item">${order.shippingAddress.city}, ${order.shippingAddress.state} ${order.shippingAddress.zipCode}</div>
              </div>
            ` : ''}
          </div>
        `).join('')}

        <div class="footer">
          <p>Total Orders: ${orders.length} | Total Spent: $${formatPrice(totalSpent)}</p>
          <p>This is an official order history report from Hive Campus</p>
        </div>
      </body>
      </html>
    `;

    // Create a new window for printing
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(printContent);
      printWindow.document.close();

      // Wait for content to load then print
      printWindow.onload = () => {
        printWindow.print();
        // Close the window after printing (optional)
        setTimeout(() => {
          printWindow.close();
        }, 1000);
      };
    }
  };

  // Delivery tracking simulation
  const checkDeliveryStatus = async (orderId: string) => {
    // Simulate API call to check delivery status
    return new Promise<{ delivered: boolean; deliveryDate?: string }>((resolve) => {
      setTimeout(() => {
        // For testing, randomly determine if delivered (or check if enough time has passed)
        const order = orders.find(o => o.id === orderId);
        if (order && order.trackingInfo?.estimatedDeliveryDate) {
          const estimatedDate = new Date(order.trackingInfo.estimatedDeliveryDate);
          const now = new Date();
          const delivered = now >= estimatedDate;

          resolve({
            delivered,
            deliveryDate: delivered ? estimatedDate.toISOString() : undefined
          });
        } else {
          resolve({ delivered: false });
        }
      }, 1000);
    });
  };

  // Auto-detect delivery and prompt for confirmation
  const handleDeliveryDetection = async (orderId: string) => {
    try {
      const deliveryStatus = await checkDeliveryStatus(orderId);

      if (deliveryStatus.delivered) {
        // Update order status to delivered
        setOrders(prevOrders =>
          prevOrders.map(order =>
            order.id === orderId
              ? {
                  ...order,
                  status: 'delivered',
                  deliveryDetectedAt: deliveryStatus.deliveryDate
                }
              : order
          )
        );

        // Show notification to user
        alert(`📦 Delivery Detected!\n\nYour order "${orders.find(o => o.id === orderId)?.title}" has been delivered!\n\nPlease enter your delivery confirmation code to complete the order.`);

        // Auto-expand the order details to show delivery confirmation
        setExpandedOrder(orderId);
      }
    } catch (error) {
      console.error('Error checking delivery status:', error);
    }
  };

  // Simulate automatic delivery tracking (runs every 30 seconds for shipped orders)
  useEffect(() => {
    const interval = setInterval(() => {
      orders.forEach(order => {
        if (order.status === 'shipped_pending_code') {
          handleDeliveryDetection(order.id);
        }
      });
    }, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, [orders, handleDeliveryDetection]);

  // Make utility functions available globally for debugging
  (window as any).refreshOrders = refreshOrders;
  (window as any).downloadPDF = downloadOrderHistoryPDF;
  (window as any).downloadOrderPDF = downloadOrderPDF;
  (window as any).checkDelivery = handleDeliveryDetection;

  const getStatusIcon = (status: Order['status']) => {
    switch (status) {
      case 'pending_payment':
        return <Clock className="w-5 h-5 text-yellow-500" />;
      case 'payment_succeeded':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'shipped_pending_code':
        return <Package className="w-5 h-5 text-blue-500" />;
      case 'delivered':
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-700" />;
      case 'cancelled':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusText = (status: Order['status']) => {
    switch (status) {
      case 'pending_payment':
        return 'Pending Payment';
      case 'payment_succeeded':
        return 'Payment Received';
      case 'shipped_pending_code':
        return 'Shipped';
      case 'delivered':
        return 'Delivered';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return 'Unknown';
    }
  };

  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'pending_payment':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'payment_succeeded':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'shipped_pending_code':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'delivered':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const filteredOrders = orders.filter(order => {
    if (activeTab === 'active') {
      return ['pending_payment', 'payment_succeeded', 'shipped_pending_code', 'delivered'].includes(order.status);
    }
    if (activeTab === 'completed') {
      return ['completed', 'cancelled'].includes(order.status);
    }
    if (statusFilter !== 'all') {
      return order.status === statusFilter;
    }
    return true;
  });

  const totalSpent = orders
    .filter(order => order.status === 'completed')
    .reduce((sum, order) => sum + (order.total || order.amount || order.price || 0), 0);

  const totalPurchases = orders.filter(order => order.status === 'completed').length;

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 md:ml-64 flex items-center justify-center overflow-x-hidden">
        <div className="animate-pulse flex flex-col items-center">
          <div className="w-16 h-16 bg-primary-200 dark:bg-primary-700 rounded-full mb-4"></div>
          <div className="h-4 w-32 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
          <div className="text-sm text-gray-500 dark:text-gray-400">Loading your orders...</div>
        </div>
      </div>
    );
  }



  // Debug information - simple console log without useEffect
  if (orders.length > 0 && !isLoading) {
    console.log('OrderHistory loaded with', orders.length, 'orders');
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 md:ml-64 overflow-x-hidden">
      <div className="max-w-6xl mx-auto px-2 sm:px-4 lg:px-6 xl:px-8 py-4 sm:py-6 lg:py-8 w-full">


        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <button
              onClick={() => navigate(-1)}
              className="flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
              <span>Back</span>
            </button>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Order History</h1>
          <p className="text-gray-600 dark:text-gray-400">Track your purchases and order status</p>
        </div>

        {/* Analytics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-primary-100 dark:bg-primary-900/20 rounded-xl flex items-center justify-center">
                <DollarSign className="w-6 h-6 text-primary-600 dark:text-primary-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Total Spent</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">${totalSpent.toLocaleString()}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-xl flex items-center justify-center">
                <Package className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Total Purchases</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{totalPurchases}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-xl flex items-center justify-center">
                <Clock className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Active Orders</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {orders.filter(o => ['pending_payment', 'payment_succeeded', 'shipped_pending_code', 'delivered'].includes(o.status)).length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Tabs */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6 mb-8">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            {/* Tabs */}
            <div className="flex space-x-1 bg-gray-100 dark:bg-gray-700 rounded-xl p-1">
              {[
                { id: 'all', label: 'All Orders' },
                { id: 'active', label: 'Active' },
                { id: 'completed', label: 'Completed' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                    activeTab === tab.id
                      ? 'bg-white dark:bg-gray-800 text-primary-600 dark:text-primary-400 shadow-sm'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </div>

            {/* Status Filter */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Filter className="w-4 h-4 text-gray-500" />
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="all">All Statuses</option>
                  <option value="pending_payment">Pending Payment</option>
                  <option value="payment_succeeded">Payment Received</option>
                  <option value="shipped_pending_code">Shipped</option>
                  <option value="delivered">Delivered</option>
                  <option value="completed">Completed</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>

              <div className="flex items-center space-x-2">
                <button
                  onClick={refreshOrders}
                  className="flex items-center space-x-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
                >
                  <Package className="w-4 h-4" />
                  <span>Refresh</span>
                </button>

                <button
                  onClick={downloadOrderHistoryPDF}
                  className="flex items-center space-x-2 px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
                >
                  <FileText className="w-4 h-4" />
                  <span>Download PDF</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Orders List */}
        <div className="space-y-4">
          {filteredOrders.length > 0 ? (
            filteredOrders.map((order) => (
              <div key={order.id} className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                  {/* Order Info */}
                  <div className="flex items-start space-x-4">
                    <img
                      src={order.listingImage || '/placeholder-image.jpg'}
                      alt={order.title || order.listingTitle || 'Order Item'}
                      className="w-16 h-16 rounded-xl object-cover"
                    />
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
                        {order.title || order.listingTitle || 'Order Item'}
                      </h3>
                      <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400 mb-2">
                        <User className="w-4 h-4" />
                        <span>Sold by {order.sellerName || 'Unknown Seller'}</span>
                      </div>
                      <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                        <div className="flex items-center space-x-1">
                          <Calendar className="w-4 h-4" />
                          <span>{new Date(order.createdAt).toLocaleDateString()}</span>
                        </div>
                        <span>Order #{order.id}</span>
                      </div>
                    </div>
                  </div>

                  {/* Status and Price */}
                  <div className="flex flex-col md:items-end space-y-2">
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(order.status)}
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(order.status)}`}>
                        {getStatusText(order.status)}
                      </span>
                    </div>
                    <p className="text-xl font-bold text-gray-900 dark:text-white">
                      ${formatPrice(order.total || order.amount || order.price || 0)}
                    </p>
                    <div className="flex flex-wrap items-center gap-2">
                      {/* Primary Actions */}
                      <div className="flex items-center space-x-2">
                        <Link
                          to={`/listing/${order.listingId}`}
                          className="flex items-center space-x-1 text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 text-sm"
                        >
                          <Eye className="w-4 h-4" />
                          <span>View Item</span>
                        </Link>
                        {(order.trackingInfo?.trackingNumber || order.id.startsWith('order_test_')) && (
                          <Link
                            to={`/order/${order.id}`}
                            className="flex items-center space-x-1 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm"
                          >
                            <Truck className="w-4 h-4" />
                            <span>Track Order</span>
                          </Link>
                        )}
                        <button
                          onClick={() => setExpandedOrder(expandedOrder === order.id ? null : order.id)}
                          className="text-gray-600 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 text-sm font-medium"
                        >
                          {expandedOrder === order.id ? 'Hide Details' : 'Order Details'}
                        </button>
                        <button
                          onClick={() => downloadOrderPDF(order)}
                          className="flex items-center space-x-1 text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 text-sm font-medium"
                          title="Download order receipt"
                        >
                          <FileText className="w-4 h-4" />
                          <span>PDF</span>
                        </button>
                      </div>

                      {/* Confirm Delivery Button for shipped orders */}
                      {(order.status === 'shipped_pending_code' || order.status === 'delivered') && (
                        <button
                          onClick={() => setExpandedOrder(expandedOrder === order.id ? order.id : order.id)}
                          className="flex items-center space-x-1 px-3 py-1.5 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors"
                        >
                          <Shield className="w-4 h-4" />
                          <span>Confirm Delivery</span>
                        </button>
                      )}
                    </div>
                  </div>
                </div>

                {/* Expanded Order Details */}
                {expandedOrder === order.id && (
                  <div className="border-t border-gray-200 dark:border-gray-700 p-6 bg-gray-50 dark:bg-gray-750">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      {/* Payment Summary */}
                      <div className="space-y-4">
                        <h4 className="font-semibold text-gray-900 dark:text-white flex items-center">
                          <DollarSign className="w-5 h-5 mr-2" />
                          Payment Summary
                        </h4>
                        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 space-y-2">
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600 dark:text-gray-400">Item Price</span>
                            <span className="text-gray-900 dark:text-white">${formatPrice(order.amount || order.price || 0)}</span>
                          </div>
                          {order.shippingFee && order.shippingFee > 0 && (
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-600 dark:text-gray-400">Shipping</span>
                              <span className="text-gray-900 dark:text-white">${formatPrice(order.shippingFee)}</span>
                            </div>
                          )}
                          <div className="border-t border-gray-200 dark:border-gray-700 pt-2">
                            <div className="flex justify-between font-semibold">
                              <span className="text-gray-900 dark:text-white">Total Paid</span>
                              <span className="text-gray-900 dark:text-white">${formatPrice(order.total || order.amount || order.price || 0)}</span>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2 text-sm">
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            <span className="text-green-600 dark:text-green-400">✅ Payment Completed</span>
                          </div>
                        </div>
                      </div>

                      {/* Shipping & Tracking */}
                      <div className="space-y-4">
                        <h4 className="font-semibold text-gray-900 dark:text-white flex items-center">
                          <Truck className="w-5 h-5 mr-2" />
                          Shipping & Tracking
                        </h4>
                        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 space-y-3">
                          {order.shippingAddress && (
                            <div>
                              <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400 mb-1">
                                <MapPin className="w-4 h-4" />
                                <span>Shipping Address</span>
                              </div>
                              <div className="text-sm text-gray-900 dark:text-white">
                                <div>{order.shippingAddress.firstName} {order.shippingAddress.lastName}</div>
                                <div>{order.shippingAddress.addressLine1}</div>
                                {order.shippingAddress.addressLine2 && <div>{order.shippingAddress.addressLine2}</div>}
                                <div>{order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.zipCode}</div>
                              </div>
                            </div>
                          )}

                          {order.trackingInfo?.trackingNumber && (
                            <div>
                              <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">Tracking Number</div>
                              <div className="flex items-center space-x-2">
                                <code className="text-sm bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                                  {order.trackingInfo.trackingNumber}
                                </code>
                                <button
                                  onClick={() => copyDeliveryCode(order.trackingInfo!.trackingNumber!)}
                                  className="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300"
                                >
                                  <Copy className="w-4 h-4" />
                                </button>
                              </div>
                            </div>
                          )}

                          {order.trackingInfo?.estimatedDeliveryDate && (
                            <div>
                              <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">Estimated Delivery</div>
                              <div className="text-sm text-gray-900 dark:text-white">
                                {new Date(order.trackingInfo.estimatedDeliveryDate).toLocaleDateString()}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Delivery Confirmation Section */}
                    {(order.status === 'shipped_pending_code' || order.status === 'delivered') && (
                      <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                        <div className="flex items-start space-x-3">
                          <Lock className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5" />
                          <div className="flex-1">
                            <h5 className="font-semibold text-blue-900 dark:text-blue-200 mb-2">
                              {order.status === 'delivered' ? '📦 Package Delivered - Confirm Receipt' : '🔐 Delivery Confirmation Required'}
                            </h5>

                            {/* Delivery Status */}
                            {order.status === 'delivered' && (
                              <div className="mb-3 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                                <div className="flex items-center space-x-2">
                                  <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
                                  <div>
                                    <p className="font-semibold text-green-900 dark:text-green-200">📦 Delivery Detected!</p>
                                    <p className="text-sm text-green-800 dark:text-green-300">
                                      Our system has detected that your package was delivered. Please confirm receipt with your delivery code.
                                    </p>
                                    {order.deliveryDetectedAt && (
                                      <p className="text-xs text-green-700 dark:text-green-400 mt-1">
                                        Detected on: {new Date(order.deliveryDetectedAt).toLocaleDateString()} at {new Date(order.deliveryDetectedAt).toLocaleTimeString()}
                                      </p>
                                    )}
                                  </div>
                                </div>
                              </div>
                            )}

                            <p className="text-sm text-blue-800 dark:text-blue-300 mb-4">
                              {order.status === 'delivered'
                                ? 'Your package has been delivered! Enter the 6-digit delivery code to confirm receipt and complete your order.'
                                : 'Enter the 6-digit delivery code you received when the package was delivered to confirm receipt and release funds to the seller.'
                              }
                            </p>

                            {/* Delivery Tracking Status */}
                            <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-2">
                                  <Truck className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Delivery Tracking</span>
                                </div>
                                <button
                                  onClick={() => handleDeliveryDetection(order.id)}
                                  className="text-xs px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors"
                                >
                                  Check Status
                                </button>
                              </div>
                              <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                                🤖 Hive Campus automatically tracks your delivery and will notify you when your package arrives.
                              </p>
                            </div>

                            {/* Quick Action Buttons */}
                            <div className="mb-4 flex flex-wrap gap-2">
                              <Link
                                to={`/listing/${order.listingId}`}
                                className="flex items-center space-x-1 px-3 py-1.5 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 hover:bg-blue-200 dark:hover:bg-blue-900/50 rounded-lg text-sm transition-colors"
                              >
                                <Eye className="w-4 h-4" />
                                <span>View Item</span>
                              </Link>
                              {(order.trackingInfo?.trackingNumber || order.id.startsWith('order_test_')) && (
                                <Link
                                  to={`/order/${order.id}`}
                                  className="flex items-center space-x-1 px-3 py-1.5 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 hover:bg-blue-200 dark:hover:bg-blue-900/50 rounded-lg text-sm transition-colors"
                                >
                                  <Truck className="w-4 h-4" />
                                  <span>Track Order</span>
                                </Link>
                              )}
                              <button
                                onClick={() => setExpandedOrder(expandedOrder === order.id ? null : order.id)}
                                className="flex items-center space-x-1 px-3 py-1.5 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 hover:bg-blue-200 dark:hover:bg-blue-900/50 rounded-lg text-sm transition-colors"
                              >
                                <Package className="w-4 h-4" />
                                <span>Order Details</span>
                              </button>
                            </div>

                            {/* Show delivery code for testing */}
                            {order.id.startsWith('order_test_') && (order.deliveryCode || order.secretCode) && (
                              <div className="mb-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                                <div className="text-sm text-yellow-800 dark:text-yellow-200 mb-2">
                                  🧪 <strong>Testing Mode</strong> - Your delivery code:
                                </div>
                                <div className="flex items-center space-x-2">
                                  <code className="text-lg font-mono font-bold bg-yellow-100 dark:bg-yellow-800 px-3 py-1 rounded">
                                    {order.deliveryCode || order.secretCode}
                                  </code>
                                  <button
                                    onClick={() => copyDeliveryCode(order.deliveryCode || order.secretCode || '')}
                                    className="text-yellow-700 dark:text-yellow-300 hover:text-yellow-800 dark:hover:text-yellow-200"
                                    title="Copy code"
                                  >
                                    <Copy className="w-4 h-4" />
                                  </button>
                                </div>
                              </div>
                            )}

                            <div className="flex items-center space-x-3">
                              <input
                                type="text"
                                placeholder="Enter 6-digit code"
                                maxLength={6}
                                value={secretCode[order.id] || ''}
                                onChange={(e) => setSecretCode({ ...secretCode, [order.id]: e.target.value.replace(/\D/g, '') })}
                                className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-center font-mono text-lg"
                              />
                              <button
                                onClick={() => handleSubmitDeliveryCode(order.id)}
                                disabled={submittingCode === order.id || !secretCode[order.id] || secretCode[order.id].length !== 6}
                                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors"
                              >
                                {submittingCode === order.id ? 'Verifying...' : 'Confirm Delivery'}
                              </button>
                            </div>

                            {codeErrors[order.id] && (
                              <div className="mt-2 flex items-center space-x-2 text-sm text-red-600 dark:text-red-400">
                                <AlertCircle className="w-4 h-4" />
                                <span>{codeErrors[order.id]}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Completed Order Message */}
                    {order.status === 'completed' && (
                      <div className="mt-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
                          <div>
                            <h5 className="font-semibold text-green-900 dark:text-green-200">✅ Order Completed</h5>
                            <p className="text-sm text-green-800 dark:text-green-300">
                              Delivery confirmed! Funds have been released to the seller.
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))
          ) : (
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-12 text-center">
              <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">No Orders Found</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                {activeTab === 'active' 
                  ? "You don't have any active orders at the moment."
                  : activeTab === 'completed'
                  ? "You haven't completed any orders yet."
                  : "You haven't placed any orders yet."
                }
              </p>
              <Link
                to="/"
                className="inline-flex items-center space-x-2 bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg transition-colors"
              >
                <span>Start Shopping</span>
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OrderHistory;
