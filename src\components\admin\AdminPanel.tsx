import React, { useState, useEffect } from 'react';
import { Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { isUserAdmin, logAdminAction, AdminSession } from '../../utils/adminAuth';
import AdminSidebar from './AdminSidebar';
import AdminHeader from './AdminHeader';
import AdminPinAuth from './AdminPinAuth';
import AdminOverview from './pages/AdminOverview';
import AdminUsers from './pages/AdminUsers';
import AdminUniversities from './pages/AdminUniversities';
import AdminListings from './pages/AdminListings';
import AdminTransactions from './pages/AdminTransactions';
import AdminChat from './pages/AdminChat';
import AdminShipping from './pages/AdminShipping';
import AdminAnalytics from './pages/AdminAnalytics';
import AdminReports from './pages/AdminReports';
import AdminReeFlex from './pages/AdminReeFlex';
import AdminSettings from './pages/AdminSettings';
import AdminWalletSettings from './pages/AdminWalletSettings';
import AdminWalletReports from './pages/AdminWalletReports';
import AdminPinTest from './AdminPinTest';
import { AdminThemeProvider } from './AdminThemeContext';

const AdminPanel: React.FC = () => {
  const { currentUser, userProfile, isAdmin, isLoading } = useAuth();
  const location = useLocation();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [adminVerified, setAdminVerified] = useState(false);
  const [verificationLoading, setVerificationLoading] = useState(true);
  const [pinVerified, setPinVerified] = useState(false);
  const [checkingPin, setCheckingPin] = useState(true);

  // Force clear PIN verification on component mount for testing
  useEffect(() => {
    console.log('=== ADMIN PANEL MOUNTED ===');
    console.log('Forcing PIN verification clear for testing...');
    sessionStorage.removeItem('adminPinVerified');
    sessionStorage.removeItem('adminPinTime');
    setPinVerified(false);
  }, []);

  // Enhanced admin verification
  useEffect(() => {
    const verifyAdminAccess = async () => {
      if (!currentUser || !userProfile) {
        setVerificationLoading(false);
        return;
      }

      try {
        // Double-check admin status
        const isVerifiedAdmin = await isUserAdmin(currentUser);
        setAdminVerified(isVerifiedAdmin);

        if (isVerifiedAdmin && userProfile) {
          // Log admin panel access
          await logAdminAction(userProfile, 'admin_panel_access', {
            path: location.pathname,
            timestamp: new Date().toISOString()
          });

          // Initialize admin session
          const adminSession = AdminSession.getInstance();
          adminSession.setSessionData('lastAccess', new Date().toISOString());
          adminSession.setSessionData('currentPath', location.pathname);
        }
      } catch (error) {
        console.error('Error verifying admin access:', error);
        setAdminVerified(false);
      } finally {
        setVerificationLoading(false);
      }
    };

    verifyAdminAccess();
  }, [currentUser, userProfile, location.pathname]);

  useEffect(() => {
    // Check if PIN is already verified in this session
    const checkPinVerification = () => {
      const pinVerified = sessionStorage.getItem('adminPinVerified');
      const pinTime = sessionStorage.getItem('adminPinTime');

      console.log('=== PIN VERIFICATION CHECK ===');
      console.log('Raw sessionStorage values:', { pinVerified, pinTime });
      console.log('adminVerified:', adminVerified);
      console.log('currentUser:', currentUser?.email);

      if (pinVerified === 'true' && pinTime) {
        const timeDiff = Date.now() - parseInt(pinTime);
        const minutesAgo = Math.floor(timeDiff / (1000 * 60));
        console.log(`PIN was verified ${minutesAgo} minutes ago`);

        // PIN verification expires after 4 hours
        if (timeDiff < 4 * 60 * 60 * 1000) {
          console.log('✅ PIN still valid, granting access');
          setPinVerified(true);
        } else {
          console.log('❌ PIN expired, clearing session');
          sessionStorage.removeItem('adminPinVerified');
          sessionStorage.removeItem('adminPinTime');
          setPinVerified(false);
        }
      } else {
        console.log('❌ No valid PIN verification found - SHOULD SHOW PIN SCREEN');
        setPinVerified(false);
      }
      console.log('Setting checkingPin to false');
      setCheckingPin(false);
    };

    if (adminVerified) {
      console.log('Admin verified, checking PIN...');
      checkPinVerification();
    } else {
      console.log('Admin not verified, skipping PIN check');
      setCheckingPin(false);
    }
  }, [adminVerified]);

  const handlePinVerified = () => {
    setPinVerified(true);
  };

  const clearPinVerification = () => {
    console.log('=== MANUALLY CLEARING PIN VERIFICATION ===');
    sessionStorage.removeItem('adminPinVerified');
    sessionStorage.removeItem('adminPinTime');
    setPinVerified(false);
    setCheckingPin(false);
    console.log('PIN verification cleared, should show PIN screen');
  };

  // Add keyboard shortcut to clear PIN (Ctrl+Shift+P)
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.shiftKey && event.key === 'P') {
        event.preventDefault();
        clearPinVerification();
        alert('PIN verification cleared. You will need to re-enter your PIN.');
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Show loading state
  if (isLoading || verificationLoading || checkingPin) {
    console.log('AdminPanel loading state:', { isLoading, verificationLoading, checkingPin });
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          <p className="text-gray-600 dark:text-gray-400">
            {checkingPin ? 'Checking security...' : 'Verifying admin access...'}
          </p>
        </div>
      </div>
    );
  }

  // Redirect non-admin users
  if (!isAdmin || !adminVerified) {
    console.log('Redirecting non-admin user:', { isAdmin, adminVerified });
    return <Navigate to="/home" replace />;
  }

  // Show PIN authentication if not verified
  console.log('PIN verification status:', {
    pinVerified: Boolean(pinVerified),
    checkingPin: Boolean(checkingPin),
    pinVerifiedType: typeof pinVerified,
    pinVerifiedValue: pinVerified
  });
  if (!pinVerified) {
    console.log('Showing PIN authentication screen');
    return <AdminPinAuth onPinVerified={handlePinVerified} />;
  }

  return (
    <AdminThemeProvider>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* Development Debug Panel */}
        {process.env.NODE_ENV === 'development' && (
          <div className="fixed top-0 right-0 z-50 bg-red-600 text-white p-2 text-xs">
            <div>PIN: {pinVerified ? '✅' : '❌'}</div>
            <div>Checking: {checkingPin ? '⏳' : '✅'}</div>
            <button
              onClick={clearPinVerification}
              className="bg-red-800 px-2 py-1 rounded text-xs mt-1"
            >
              Clear PIN
            </button>
          </div>
        )}

        {/* Admin Sidebar */}
        <AdminSidebar
          isOpen={sidebarOpen}
          onClose={() => setSidebarOpen(false)}
        />

        {/* Main Content Area */}
        <div className="lg:pl-64">
          {/* Admin Header */}
          <AdminHeader 
            onMenuClick={() => setSidebarOpen(true)}
            currentPath={location.pathname}
          />

          {/* Page Content */}
          <main className="py-6">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <Routes>
                <Route index element={<AdminOverview />} />
                <Route path="dashboard" element={<AdminOverview />} />
                <Route path="overview" element={<AdminOverview />} />
                <Route path="users" element={<AdminUsers />} />
                <Route path="universities" element={<AdminUniversities />} />
                <Route path="listings" element={<AdminListings />} />
                <Route path="transactions" element={<AdminTransactions />} />
                <Route path="chat" element={<AdminChat />} />
                <Route path="shipping" element={<AdminShipping />} />
                <Route path="analytics" element={<AdminAnalytics />} />
                <Route path="reports" element={<AdminReports />} />
                <Route path="reeflex" element={<AdminReeFlex />} />
                <Route path="settings" element={<AdminSettings />} />
                <Route path="wallet-settings" element={<AdminWalletSettings />} />
                <Route path="wallet-reports" element={<AdminWalletReports />} />
                {/* Development only route */}
                {process.env.NODE_ENV === 'development' && (
                  <Route path="pin-test" element={<AdminPinTest />} />
                )}
                <Route path="*" element={<Navigate to="/admin/dashboard" replace />} />
              </Routes>
            </div>
          </main>
        </div>

        {/* Mobile sidebar overlay */}
        {sidebarOpen && (
          <div 
            className="fixed inset-0 z-40 lg:hidden"
            onClick={() => setSidebarOpen(false)}
          >
            <div className="fixed inset-0 bg-gray-600 bg-opacity-75"></div>
          </div>
        )}
      </div>
    </AdminThemeProvider>
  );
};

export default AdminPanel;
