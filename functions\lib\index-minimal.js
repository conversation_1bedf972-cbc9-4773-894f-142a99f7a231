"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.redeemSecretCode = exports.stripeWebhook = exports.createUserRecord = exports.testFunction = void 0;
// Minimal Firebase Functions for Hive Campus
const functions = __importStar(require("firebase-functions/v1"));
const admin = __importStar(require("firebase-admin"));
// Initialize Firebase Admin
if (!admin.apps.length) {
    admin.initializeApp();
}
// Simple test function
exports.testFunction = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 60
})
    .https.onRequest(async (_req, res) => {
    res.json({
        success: true,
        message: 'Test function working',
        timestamp: new Date().toISOString()
    });
});
// Enhanced user creation function with complete signup data
exports.createUserRecord = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 60
})
    .auth.user().onCreate(async (user) => {
    try {
        const { uid, email, displayName } = user;
        if (!email) {
            throw new Error('User email is required');
        }
        // Verify educational email
        const isEducationalEmail = (email) => {
            return email.endsWith('.edu') ||
                email.includes('@outlook.') ||
                email.includes('@hotmail.') ||
                email.includes('@live.') ||
                email.includes('@student.');
        };
        if (!isEducationalEmail(email)) {
            await admin.auth().deleteUser(uid);
            throw new Error('Only .edu email addresses are allowed to register');
        }
        // Extract university from email domain
        const emailParts = email.split('@');
        const domain = emailParts[1];
        let university = domain.split('.')[0];
        university = university.charAt(0).toUpperCase() + university.slice(1);
        // Determine role based on email
        const isAdminEmail = email === '<EMAIL>';
        const userRole = isAdminEmail ? 'admin' : 'student';
        // Create user document
        const userData = {
            uid,
            name: displayName || email.split('@')[0],
            email,
            role: userRole,
            university,
            emailVerified: true,
            status: 'active',
            createdAt: admin.firestore.Timestamp.now(),
            updatedAt: admin.firestore.Timestamp.now()
        };
        await admin.firestore().collection('users').doc(uid).set(userData);
        // Set custom claims for admin users
        if (isAdminEmail) {
            await admin.auth().setCustomUserClaims(uid, { role: 'admin' });
            console.log('Admin role set for user:', email);
        }
        else {
            await admin.auth().setCustomUserClaims(uid, { role: 'student' });
        }
        // Initialize wallet
        try {
            await admin.firestore().collection('users').doc(uid).collection('wallet').doc('balance').set({
                balance: 0,
                referralCode: uid.substring(0, 8).toUpperCase(),
                usedReferral: false,
                createdAt: admin.firestore.Timestamp.now(),
                lastUpdated: admin.firestore.Timestamp.now()
            });
        }
        catch (walletError) {
            console.error('Error initializing wallet:', walletError);
        }
        return { success: true };
    }
    catch (error) {
        console.error('Error creating user record:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return { success: false, error: errorMessage };
    }
});
// Stripe webhook handler - lazy load Stripe to avoid initialization timeout
exports.stripeWebhook = functions
    .runWith({
    memory: '512MB',
    timeoutSeconds: 300
})
    .https.onRequest(async (req, res) => {
    try {
        // Lazy load Stripe
        const Stripe = (await Promise.resolve().then(() => __importStar(require('stripe')))).default;
        const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '***********************************************************************************************************', {
            apiVersion: '2025-05-28.basil',
        });
        const WEBHOOK_SECRET = 'whsec_rEz20Kue3bkGHfmnaZilP01s614ULBQb';
        const sig = req.headers['stripe-signature'];
        if (!sig) {
            console.error('No Stripe signature found');
            res.status(400).send('No Stripe signature');
            return;
        }
        let event;
        try {
            event = stripe.webhooks.constructEvent(req.body, sig, WEBHOOK_SECRET);
        }
        catch (err) {
            console.error('Webhook signature verification failed:', err.message);
            res.status(400).send(`Webhook Error: ${err.message}`);
            return;
        }
        console.log('Processing webhook event:', event.type);
        // Generate 6-digit secret code
        const generateSecretCode = () => {
            return Math.floor(100000 + Math.random() * 900000).toString();
        };
        // Handle checkout session completed
        if (event.type === 'checkout.session.completed') {
            const session = event.data.object;
            const metadata = session.metadata;
            if (metadata && metadata.orderId) {
                const orderId = metadata.orderId;
                const orderRef = admin.firestore().collection('orders').doc(orderId);
                const orderDoc = await orderRef.get();
                if (orderDoc.exists) {
                    const secretCode = generateSecretCode();
                    // Update order with payment completion and secret code
                    await orderRef.update({
                        status: 'payment_completed',
                        stripeSessionId: session.id,
                        stripePaymentIntentId: session.payment_intent,
                        secretCode: secretCode,
                        paymentCompletedAt: admin.firestore.Timestamp.now(),
                        updatedAt: admin.firestore.Timestamp.now()
                    });
                    console.log('Order completion processed successfully:', orderId);
                }
            }
        }
        res.status(200).json({ received: true });
    }
    catch (error) {
        console.error('Error processing webhook:', error);
        res.status(500).json({ error: 'Webhook processing failed' });
    }
});
// Secret code redemption function
exports.redeemSecretCode = functions
    .runWith({
    memory: '512MB',
    timeoutSeconds: 300
})
    .https.onCall(async (data, context) => {
    try {
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const { orderId, secretCode } = data;
        const userId = context.auth.uid;
        if (!orderId || !secretCode) {
            throw new functions.https.HttpsError('invalid-argument', 'Order ID and secret code are required');
        }
        // Get order document
        const orderRef = admin.firestore().collection('orders').doc(orderId);
        const orderDoc = await orderRef.get();
        if (!orderDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Order not found');
        }
        const orderData = orderDoc.data();
        if (!orderData) {
            throw new functions.https.HttpsError('not-found', 'Order data not found');
        }
        // Verify buyer is the one redeeming
        if (orderData.buyerId !== userId) {
            throw new functions.https.HttpsError('permission-denied', 'Only the buyer can redeem the secret code');
        }
        // Verify secret code
        if (orderData.secretCode !== secretCode) {
            throw new functions.https.HttpsError('invalid-argument', 'Invalid secret code');
        }
        // Check if already redeemed
        if (orderData.status === 'completed') {
            throw new functions.https.HttpsError('failed-precondition', 'Order has already been completed');
        }
        // Update order status to completed
        await orderRef.update({
            status: 'completed',
            secretCodeRedeemedAt: admin.firestore.Timestamp.now(),
            completedAt: admin.firestore.Timestamp.now(),
            updatedAt: admin.firestore.Timestamp.now()
        });
        return {
            success: true,
            message: 'Secret code redeemed successfully',
            orderId: orderId
        };
    }
    catch (error) {
        console.error('Error redeeming secret code:', error);
        if (error instanceof functions.https.HttpsError) {
            throw error;
        }
        throw new functions.https.HttpsError('internal', 'Failed to redeem secret code');
    }
});
