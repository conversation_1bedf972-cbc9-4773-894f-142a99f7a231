console.log('🚀 Testing Stripe Webhook...');

const WEBHOOK_URL = 'https://us-central1-h1c1-798a8.cloudfunctions.net/stripeWebhook';

async function testWebhook() {
  try {
    console.log('📡 Testing webhook endpoint...');
    
    const response = await fetch(WEBHOOK_URL, {
      method: 'GET'
    });
    
    console.log(`Status: ${response.status}`);
    console.log(`Status Text: ${response.statusText}`);
    
    if (response.status === 405) {
      console.log('✅ Webhook endpoint is working (405 Method Not Allowed for GET)');
    } else {
      console.log('⚠️ Unexpected response');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

testWebhook();
