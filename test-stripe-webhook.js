// Test script for Stripe webhook functionality
const crypto = require('crypto');

// Mock Stripe webhook event for testing
const mockCheckoutSessionCompleted = {
  id: 'evt_test_webhook',
  object: 'event',
  api_version: '2025-05-28.basil',
  created: Math.floor(Date.now() / 1000),
  data: {
    object: {
      id: 'cs_test_checkout_session',
      object: 'checkout.session',
      amount_total: 2599, // $25.99 in cents
      currency: 'usd',
      customer: null,
      customer_email: '<EMAIL>',
      metadata: {
        orderId: 'test-order-123',
        listingId: 'test-listing-456',
        buyerId: 'test-buyer-456',
        sellerId: 'test-seller-123',
        cashbackAmount: '0.52'
      },
      payment_intent: 'pi_test_payment_intent',
      payment_status: 'paid',
      status: 'complete'
    }
  },
  livemode: false,
  pending_webhooks: 1,
  request: {
    id: 'req_test_request',
    idempotency_key: null
  },
  type: 'checkout.session.completed'
};

const mockPaymentIntentSucceeded = {
  id: 'evt_test_webhook_2',
  object: 'event',
  api_version: '2025-05-28.basil',
  created: Math.floor(Date.now() / 1000),
  data: {
    object: {
      id: 'pi_test_payment_intent',
      object: 'payment_intent',
      amount: 2599,
      currency: 'usd',
      customer: null,
      metadata: {
        orderId: 'test-order-123',
        listingId: 'test-listing-456',
        buyerId: 'test-buyer-456',
        sellerId: 'test-seller-123',
        cashbackAmount: '0.52'
      },
      status: 'succeeded'
    }
  },
  livemode: false,
  pending_webhooks: 1,
  request: {
    id: 'req_test_request_2',
    idempotency_key: null
  },
  type: 'payment_intent.succeeded'
};

// Function to create a mock Stripe signature
function createMockStripeSignature(payload, secret) {
  const timestamp = Math.floor(Date.now() / 1000);
  const payloadString = JSON.stringify(payload);
  const signedPayload = `${timestamp}.${payloadString}`;
  const signature = crypto
    .createHmac('sha256', secret)
    .update(signedPayload, 'utf8')
    .digest('hex');
  
  return `t=${timestamp},v1=${signature}`;
}

async function testWebhookWithMockEvent(event, eventType) {
  try {
    console.log(`🧪 Testing webhook with ${eventType} event...`);
    
    const webhookUrl = 'https://us-central1-h1c1-798a8.cloudfunctions.net/stripeWebhook';
    const webhookSecret = 'whsec_Ggd0wEt8z8NzRV5kyEyvXH2iB1xrWSZq';
    
    const payload = JSON.stringify(event);
    const signature = createMockStripeSignature(event, webhookSecret);
    
    console.log(`📡 Sending ${eventType} to webhook...`);
    
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Stripe-Signature': signature
      },
      body: payload
    });
    
    const responseText = await response.text();
    
    console.log(`📊 Response Status: ${response.status}`);
    console.log(`📄 Response Body: ${responseText}`);
    
    if (response.status === 200) {
      console.log(`✅ ${eventType} webhook test passed!`);
      return true;
    } else {
      console.log(`❌ ${eventType} webhook test failed!`);
      return false;
    }
    
  } catch (error) {
    console.error(`❌ Error testing ${eventType} webhook:`, error);
    return false;
  }
}

async function createTestOrder() {
  try {
    console.log('📦 Creating test order for webhook testing...');
    
    const admin = require('firebase-admin');
    
    // Initialize Firebase Admin if not already done
    if (!admin.apps.length) {
      const serviceAccount = require('./functions/service-account-key.json');
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: 'h1c1-798a8'
      });
    }
    
    const db = admin.firestore();
    const orderId = 'test-order-123';
    
    const testOrder = {
      id: orderId,
      listingId: 'test-listing-456',
      listingTitle: 'Test Textbook - Webhook Test',
      buyerId: 'test-buyer-456',
      sellerId: 'test-seller-123',
      quantity: 1,
      itemPrice: 25.99,
      subtotal: 25.99,
      commissionAmount: 1.30,
      sellerAmount: 24.69,
      cashbackAmount: 0.52,
      totalAmount: 25.99,
      status: 'pending_payment',
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now()
    };
    
    await db.collection('orders').doc(orderId).set(testOrder);
    console.log(`✅ Test order created: ${orderId}`);
    
    return orderId;
    
  } catch (error) {
    console.error('❌ Error creating test order:', error);
    throw error;
  }
}

async function checkOrderStatus(orderId) {
  try {
    console.log(`🔍 Checking order status: ${orderId}`);
    
    const admin = require('firebase-admin');
    const db = admin.firestore();
    
    const orderDoc = await db.collection('orders').doc(orderId).get();
    
    if (orderDoc.exists) {
      const orderData = orderDoc.data();
      console.log(`📋 Order Status: ${orderData.status}`);
      console.log(`💰 Total Amount: $${orderData.totalAmount}`);
      
      if (orderData.secretCode) {
        console.log(`🔐 Secret Code: ${orderData.secretCode}`);
      }
      
      if (orderData.stripeSessionId) {
        console.log(`💳 Stripe Session ID: ${orderData.stripeSessionId}`);
      }
      
      return orderData;
    } else {
      console.log('❌ Order not found');
      return null;
    }
    
  } catch (error) {
    console.error('❌ Error checking order status:', error);
    return null;
  }
}

async function checkNotifications() {
  try {
    console.log('📧 Checking notifications...');
    
    const admin = require('firebase-admin');
    const db = admin.firestore();
    
    // Check user notifications
    const userNotifications = await db.collection('notifications')
      .where('userId', '==', 'test-buyer-456')
      .orderBy('createdAt', 'desc')
      .limit(5)
      .get();
    
    console.log(`📱 User notifications: ${userNotifications.size}`);
    userNotifications.forEach(doc => {
      const notification = doc.data();
      console.log(`  - ${notification.type}: ${notification.title}`);
    });
    
    // Check admin notifications
    const adminNotifications = await db.collection('adminNotifications')
      .orderBy('createdAt', 'desc')
      .limit(5)
      .get();
    
    console.log(`👨‍💼 Admin notifications: ${adminNotifications.size}`);
    adminNotifications.forEach(doc => {
      const notification = doc.data();
      console.log(`  - ${notification.type}: ${notification.title}`);
    });
    
  } catch (error) {
    console.error('❌ Error checking notifications:', error);
  }
}

async function runWebhookTests() {
  try {
    console.log('🚀 Starting Stripe webhook tests...\n');
    
    // Step 1: Create test order
    const orderId = await createTestOrder();
    console.log('');
    
    // Step 2: Test checkout.session.completed webhook
    const checkoutSuccess = await testWebhookWithMockEvent(
      mockCheckoutSessionCompleted, 
      'checkout.session.completed'
    );
    console.log('');
    
    // Step 3: Test payment_intent.succeeded webhook
    const paymentSuccess = await testWebhookWithMockEvent(
      mockPaymentIntentSucceeded, 
      'payment_intent.succeeded'
    );
    console.log('');
    
    // Step 4: Check order status after webhook processing
    await checkOrderStatus(orderId);
    console.log('');
    
    // Step 5: Check notifications
    await checkNotifications();
    console.log('');
    
    // Summary
    console.log('📊 Test Results Summary:');
    console.log(`✅ Checkout Session Webhook: ${checkoutSuccess ? 'PASSED' : 'FAILED'}`);
    console.log(`✅ Payment Intent Webhook: ${paymentSuccess ? 'PASSED' : 'FAILED'}`);
    
    if (checkoutSuccess && paymentSuccess) {
      console.log('\n🎉 All webhook tests passed! The Stripe integration is working correctly.');
    } else {
      console.log('\n❌ Some webhook tests failed. Please check the logs for details.');
    }
    
  } catch (error) {
    console.error('❌ Webhook tests failed:', error);
  }
}

// Run the webhook tests
runWebhookTests();
