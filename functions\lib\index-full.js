"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateProfile = exports.stripeApi = exports.refreshUserToken = exports.setAdminRole = exports.redeemSecretCode = exports.stripeWebhook = exports.saveSignupData = exports.createUserRecord = exports.testFunction = void 0;
// Minimal Firebase Functions for Hive Campus
const functions = __importStar(require("firebase-functions/v1"));
const admin = __importStar(require("firebase-admin"));
// Initialize Firebase Admin
if (!admin.apps.length) {
    admin.initializeApp();
}
// Simple test function
exports.testFunction = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 60
})
    .https.onRequest(async (_req, res) => {
    res.json({
        success: true,
        message: 'Test function working',
        timestamp: new Date().toISOString()
    });
});
// Enhanced user creation function with complete signup data
exports.createUserRecord = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 60
})
    .auth.user().onCreate(async (user) => {
    try {
        const { uid, email, displayName } = user;
        if (!email) {
            throw new Error('User email is required');
        }
        // Verify educational email
        const isEducationalEmail = (email) => {
            return email.endsWith('.edu') ||
                email.includes('@outlook.') ||
                email.includes('@hotmail.') ||
                email.includes('@live.') ||
                email.includes('@student.');
        };
        if (!isEducationalEmail(email)) {
            await admin.auth().deleteUser(uid);
            throw new Error('Only .edu email addresses are allowed to register');
        }
        // Extract university from email domain
        const emailParts = email.split('@');
        const domain = emailParts[1];
        let university = domain.split('.')[0];
        university = university.charAt(0).toUpperCase() + university.slice(1);
        // Determine role based on email
        const isAdminEmail = email === '<EMAIL>';
        const userRole = isAdminEmail ? 'admin' : 'student';
        // Create user document
        const userData = {
            uid,
            name: displayName || email.split('@')[0],
            email,
            role: userRole,
            university,
            emailVerified: true,
            status: 'active',
            createdAt: admin.firestore.Timestamp.now(),
            updatedAt: admin.firestore.Timestamp.now()
        };
        await admin.firestore().collection('users').doc(uid).set(userData);
        // Set custom claims for admin users
        if (isAdminEmail) {
            await admin.auth().setCustomUserClaims(uid, { role: 'admin' });
            console.log('Admin role set for user:', email);
        }
        else {
            await admin.auth().setCustomUserClaims(uid, { role: 'student' });
        }
        // Initialize wallet
        try {
            await admin.firestore().collection('users').doc(uid).collection('wallet').doc('balance').set({
                balance: 0,
                referralCode: uid.substring(0, 8).toUpperCase(),
                usedReferral: false,
                createdAt: admin.firestore.Timestamp.now(),
                lastUpdated: admin.firestore.Timestamp.now()
            });
        }
        catch (walletError) {
            console.error('Error initializing wallet:', walletError);
        }
        return { success: true };
    }
    catch (error) {
        console.error('Error creating user record:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return { success: false, error: errorMessage };
    }
});
// Function to save complete signup data
exports.saveSignupData = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 60
})
    .https.onCall(async (data, context) => {
    try {
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const { firstName, lastName, university, graduationYear, dateOfBirth, phone } = data;
        const uid = context.auth.uid;
        if (!firstName || !lastName || !university) {
            throw new functions.https.HttpsError('invalid-argument', 'Missing required signup data');
        }
        // Calculate graduation year if not provided
        let calculatedGradYear = graduationYear;
        if (!calculatedGradYear && dateOfBirth) {
            const birthDate = new Date(dateOfBirth);
            const currentYear = new Date().getFullYear();
            const age = currentYear - birthDate.getFullYear();
            calculatedGradYear = currentYear + Math.max(0, 22 - age);
        }
        // Update user document
        const updateData = {
            name: `${firstName} ${lastName}`.trim(),
            firstName,
            lastName,
            university,
            graduationYear: calculatedGradYear || null,
            dateOfBirth: dateOfBirth || null,
            phone: phone || null,
            profileCompleted: true,
            updatedAt: admin.firestore.Timestamp.now()
        };
        await admin.firestore().collection('users').doc(uid).update(updateData);
        // Update Auth displayName
        await admin.auth().updateUser(uid, {
            displayName: `${firstName} ${lastName}`.trim()
        });
        return {
            success: true,
            message: 'Signup data saved successfully',
            data: updateData
        };
    }
    catch (error) {
        console.error('Error saving signup data:', error);
        if (error instanceof functions.https.HttpsError) {
            throw error;
        }
        throw new functions.https.HttpsError('internal', 'Failed to save signup data');
    }
});
// Stripe webhook handler - lazy load Stripe to avoid initialization timeout
exports.stripeWebhook = functions
    .runWith({
    memory: '512MB',
    timeoutSeconds: 300
})
    .https.onRequest(async (req, res) => {
    try {
        // Lazy load Stripe
        const Stripe = (await Promise.resolve().then(() => __importStar(require('stripe')))).default;
        const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '***********************************************************************************************************', {
            apiVersion: '2025-05-28.basil',
        });
        const WEBHOOK_SECRET = 'whsec_rEz20Kue3bkGHfmnaZilP01s614ULBQb';
        const sig = req.headers['stripe-signature'];
        if (!sig) {
            console.error('No Stripe signature found');
            res.status(400).send('No Stripe signature');
            return;
        }
        let event;
        try {
            event = stripe.webhooks.constructEvent(req.body, sig, WEBHOOK_SECRET);
        }
        catch (err) {
            console.error('Webhook signature verification failed:', err.message);
            res.status(400).send(`Webhook Error: ${err.message}`);
            return;
        }
        console.log('Processing webhook event:', event.type);
        // Generate 6-digit secret code
        const generateSecretCode = () => {
            return Math.floor(100000 + Math.random() * 900000).toString();
        };
        // Handle checkout session completed
        if (event.type === 'checkout.session.completed') {
            const session = event.data.object;
            const metadata = session.metadata;
            if (metadata && metadata.orderId) {
                const orderId = metadata.orderId;
                const orderRef = admin.firestore().collection('orders').doc(orderId);
                const orderDoc = await orderRef.get();
                if (orderDoc.exists) {
                    const secretCode = generateSecretCode();
                    // Update order with payment completion and secret code
                    await orderRef.update({
                        status: 'payment_completed',
                        stripeSessionId: session.id,
                        stripePaymentIntentId: session.payment_intent,
                        secretCode: secretCode,
                        paymentCompletedAt: admin.firestore.Timestamp.now(),
                        updatedAt: admin.firestore.Timestamp.now()
                    });
                    console.log('Order completion processed successfully:', orderId);
                }
            }
        }
        res.status(200).json({ received: true });
    }
    catch (error) {
        console.error('Error processing webhook:', error);
        res.status(500).json({ error: 'Webhook processing failed' });
    }
});
// Secret code redemption function
exports.redeemSecretCode = functions
    .runWith({
    memory: '512MB',
    timeoutSeconds: 300
})
    .https.onCall(async (data, context) => {
    var _a;
    try {
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const { orderId, secretCode } = data;
        const userId = context.auth.uid;
        if (!orderId || !secretCode) {
            throw new functions.https.HttpsError('invalid-argument', 'Order ID and secret code are required');
        }
        // Get order document
        const orderRef = admin.firestore().collection('orders').doc(orderId);
        const orderDoc = await orderRef.get();
        if (!orderDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Order not found');
        }
        const orderData = orderDoc.data();
        if (!orderData) {
            throw new functions.https.HttpsError('not-found', 'Order data not found');
        }
        // Verify buyer is the one redeeming
        if (orderData.buyerId !== userId) {
            throw new functions.https.HttpsError('permission-denied', 'Only the buyer can redeem the secret code');
        }
        // Verify secret code
        if (orderData.secretCode !== secretCode) {
            throw new functions.https.HttpsError('invalid-argument', 'Invalid secret code');
        }
        // Check if already redeemed
        if (orderData.status === 'completed') {
            throw new functions.https.HttpsError('failed-precondition', 'Order has already been completed');
        }
        // Calculate seller payout (subtract platform fee)
        const platformFeeRate = orderData.category === 'textbooks' ? 0.08 : 0.10;
        const platformFee = orderData.amount * platformFeeRate;
        const sellerPayout = orderData.amount - platformFee;
        // Release funds to seller via Stripe Transfer
        let transferId = null;
        try {
            // Get seller's Stripe Connect account
            const connectAccountDoc = await admin.firestore()
                .collection('connectAccounts')
                .doc(orderData.sellerId)
                .get();
            if (connectAccountDoc.exists && ((_a = connectAccountDoc.data()) === null || _a === void 0 ? void 0 : _a.stripeAccountId)) {
                const stripeAccountId = connectAccountDoc.data().stripeAccountId;
                // Lazy load Stripe for transfer
                const Stripe = (await Promise.resolve().then(() => __importStar(require('stripe')))).default;
                const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '***********************************************************************************************************', {
                    apiVersion: '2025-05-28.basil',
                });
                const transfer = await stripe.transfers.create({
                    amount: Math.round(sellerPayout * 100), // Convert to cents
                    currency: 'usd',
                    destination: stripeAccountId,
                    metadata: {
                        orderId: orderId,
                        buyerId: orderData.buyerId,
                        sellerId: orderData.sellerId
                    }
                });
                transferId = transfer.id;
            }
        }
        catch (transferError) {
            console.error('Error creating Stripe transfer:', transferError);
            // Continue with order completion even if transfer fails
        }
        // Update order status to completed
        await orderRef.update({
            status: 'completed',
            secretCodeRedeemedAt: admin.firestore.Timestamp.now(),
            stripeTransferId: transferId,
            sellerPayout: sellerPayout,
            platformFee: platformFee,
            completedAt: admin.firestore.Timestamp.now(),
            updatedAt: admin.firestore.Timestamp.now()
        });
        // Get seller information for notification
        const sellerDoc = await admin.firestore().collection('users').doc(orderData.sellerId).get();
        const sellerData = sellerDoc.data();
        // Send notification to seller about payout release
        if (sellerData && sellerData.email) {
            const payoutEmailSubject = 'Your payout has been released!';
            const payoutEmailBody = `
Hello ${sellerData.name || 'Seller'},

Great news! The buyer has confirmed receipt of your item, and your payout has been released.

Order Details:
- Order ID: ${orderId}
- Payout Amount: $${sellerPayout.toFixed(2)}
- Platform Fee: $${platformFee.toFixed(2)}

The funds should appear in your connected bank account within 2-7 business days.

Thank you for using Hive Campus!

Best regards,
The Hive Campus Team
        `;
            // Send email notification (placeholder - implement with your email service)
            console.log(`Email to ${sellerData.email}: ${payoutEmailSubject}\n${payoutEmailBody}`);
        }
        // Create in-app notification for seller
        await admin.firestore().collection('users').doc(orderData.sellerId).collection('notifications').add({
            type: 'payout_released',
            title: 'Payout Released',
            message: `The buyer has confirmed receipt. Your payout of $${sellerPayout.toFixed(2)} has been released.`,
            orderId: orderId,
            amount: sellerPayout,
            read: false,
            createdAt: admin.firestore.Timestamp.now()
        });
        return {
            success: true,
            message: 'Secret code redeemed successfully',
            orderId: orderId,
            sellerPayout: sellerPayout,
            transferId: transferId
        };
    }
    catch (error) {
        console.error('Error redeeming secret code:', error);
        if (error instanceof functions.https.HttpsError) {
            throw error;
        }
        throw new functions.https.HttpsError('internal', 'Failed to redeem secret code');
    }
});
// Function to set admin role using custom claims
exports.setAdminRole = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 60
})
    .https.onCall(async (data, context) => {
    try {
        // Only allow specific admin email to call this function
        if (!context.auth || context.auth.token.email !== '<EMAIL>') {
            throw new functions.https.HttpsError('permission-denied', 'Only authorized admin can set roles');
        }
        const { userId, role } = data;
        if (!userId || !role) {
            throw new functions.https.HttpsError('invalid-argument', 'User ID and role are required');
        }
        // Set custom claims
        await admin.auth().setCustomUserClaims(userId, { role: role });
        // Update user document
        await admin.firestore().collection('users').doc(userId).update({
            role: role,
            updatedAt: admin.firestore.Timestamp.now()
        });
        return {
            success: true,
            message: `Role ${role} set for user ${userId}`
        };
    }
    catch (error) {
        console.error('Error setting admin role:', error);
        if (error instanceof functions.https.HttpsError) {
            throw error;
        }
        throw new functions.https.HttpsError('internal', 'Failed to set admin role');
    }
});
// Function to refresh user token to get updated claims
exports.refreshUserToken = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 60
})
    .https.onCall(async (_data, context) => {
    try {
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        // Force token refresh by returning current claims
        const userRecord = await admin.auth().getUser(context.auth.uid);
        return {
            success: true,
            claims: userRecord.customClaims || {},
            message: 'Token refresh initiated'
        };
    }
    catch (error) {
        console.error('Error refreshing user token:', error);
        throw new functions.https.HttpsError('internal', 'Failed to refresh token');
    }
});
// Stripe API Express app for handling checkout and payments
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const stripeApp = (0, express_1.default)();
stripeApp.use((0, cors_1.default)({ origin: true }));
stripeApp.use(express_1.default.json());
// Create checkout session endpoint
stripeApp.post('/create-checkout-session', async (req, res) => {
    try {
        const { listingId, buyerId, useWalletBalance, orderDetails } = req.body;
        if (!listingId || !buyerId) {
            return res.status(400).json({ error: 'Missing required fields' });
        }
        // Get listing details
        const listingDoc = await admin.firestore().collection('listings').doc(listingId).get();
        if (!listingDoc.exists) {
            return res.status(404).json({ error: 'Listing not found' });
        }
        const listing = listingDoc.data();
        if (!listing) {
            return res.status(404).json({ error: 'Listing data not found' });
        }
        const price = (orderDetails === null || orderDetails === void 0 ? void 0 : orderDetails.price) || listing.price;
        // Calculate platform fee
        const platformFeeRate = listing.category === 'textbooks' ? 0.08 : 0.10;
        const platformFee = price * platformFeeRate;
        // Handle wallet credit
        let walletAmountUsed = 0;
        if (useWalletBalance && (orderDetails === null || orderDetails === void 0 ? void 0 : orderDetails.appliedWalletCredit)) {
            walletAmountUsed = Math.min(orderDetails.appliedWalletCredit, price);
        }
        const finalAmount = Math.max(0.50, price - walletAmountUsed); // Minimum $0.50 charge
        // Create order document first
        const orderRef = admin.firestore().collection('orders').doc();
        const order = {
            id: orderRef.id,
            buyerId,
            sellerId: listing.ownerId,
            listingId,
            amount: price,
            finalAmount,
            platformFee,
            walletAmountUsed,
            status: 'pending_payment',
            title: listing.title,
            category: listing.category,
            orderType: (orderDetails === null || orderDetails === void 0 ? void 0 : orderDetails.type) || 'buy',
            rentalPeriod: orderDetails === null || orderDetails === void 0 ? void 0 : orderDetails.period,
            bidAmount: orderDetails === null || orderDetails === void 0 ? void 0 : orderDetails.bidAmount,
            shippingAddress: orderDetails === null || orderDetails === void 0 ? void 0 : orderDetails.shippingAddress,
            deliveryMethod: (orderDetails === null || orderDetails === void 0 ? void 0 : orderDetails.deliveryMethod) || 'in_person',
            shippingFee: (orderDetails === null || orderDetails === void 0 ? void 0 : orderDetails.shippingFee) || 0,
            shippingPaidBy: (orderDetails === null || orderDetails === void 0 ? void 0 : orderDetails.shippingPaidBy) || 'buyer',
            createdAt: admin.firestore.Timestamp.now(),
            updatedAt: admin.firestore.Timestamp.now()
        };
        await orderRef.set(order);
        // Create Stripe checkout session
        const Stripe = (await Promise.resolve().then(() => __importStar(require('stripe')))).default;
        const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '***********************************************************************************************************', {
            apiVersion: '2025-05-28.basil',
        });
        const session = await stripe.checkout.sessions.create({
            payment_method_types: ['card'],
            line_items: [
                {
                    price_data: {
                        currency: 'usd',
                        product_data: {
                            name: listing.title,
                            description: listing.description || 'Hive Campus marketplace item',
                            images: listing.images ? [listing.images[0]] : []
                        },
                        unit_amount: Math.round(finalAmount * 100) // Convert to cents
                    },
                    quantity: 1
                }
            ],
            mode: 'payment',
            success_url: `${process.env.FRONTEND_URL || 'https://hivecampus.app'}/order/success?session_id={CHECKOUT_SESSION_ID}&order_id=${orderRef.id}`,
            cancel_url: `${process.env.FRONTEND_URL || 'https://hivecampus.app'}/listing/${listingId}`,
            metadata: {
                orderId: orderRef.id,
                listingId,
                buyerId,
                sellerId: listing.ownerId,
                walletAmountUsed: walletAmountUsed.toString()
            },
            automatic_tax: {
                enabled: true
            },
            shipping_address_collection: (orderDetails === null || orderDetails === void 0 ? void 0 : orderDetails.deliveryMethod) === 'mail' ? {
                allowed_countries: ['US']
            } : undefined
        });
        // Update order with session ID
        await orderRef.update({
            stripeSessionId: session.id,
            updatedAt: admin.firestore.Timestamp.now()
        });
        res.json({
            sessionId: session.id,
            sessionUrl: session.url
        });
    }
    catch (error) {
        console.error('Error creating checkout session:', error);
        res.status(500).json({ error: 'Failed to create checkout session' });
    }
});
// Export Stripe API
exports.stripeApi = functions
    .runWith({
    memory: '512MB',
    timeoutSeconds: 300
})
    .https.onRequest(stripeApp);
// Function to update user profile with proper validation
exports.updateProfile = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 60
})
    .https.onCall(async (data, context) => {
    try {
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const { name, bio, graduationYear, major, university } = data;
        const uid = context.auth.uid;
        // Validate required fields
        if (!name || !name.trim()) {
            throw new functions.https.HttpsError('invalid-argument', 'Name is required');
        }
        // Prepare update data
        const updateData = {
            name: name.trim(),
            updatedAt: admin.firestore.Timestamp.now()
        };
        if (bio !== undefined)
            updateData.bio = bio.trim();
        if (major !== undefined)
            updateData.major = major.trim();
        if (university !== undefined)
            updateData.university = university.trim();
        if (graduationYear !== undefined) {
            const year = parseInt(graduationYear);
            if (year >= 2020 && year <= 2035) {
                updateData.graduationYear = year;
            }
        }
        // Update user document
        await admin.firestore().collection('users').doc(uid).update(updateData);
        // Update Auth displayName
        await admin.auth().updateUser(uid, {
            displayName: name.trim()
        });
        return {
            success: true,
            message: 'Profile updated successfully',
            data: updateData
        };
    }
    catch (error) {
        console.error('Error updating profile:', error);
        if (error instanceof functions.https.HttpsError) {
            throw error;
        }
        throw new functions.https.HttpsError('internal', 'Failed to update profile');
    }
});
