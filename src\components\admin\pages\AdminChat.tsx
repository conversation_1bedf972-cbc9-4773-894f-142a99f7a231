import React, { useState, useEffect } from 'react';
import {
  MessageSquare,
  Search,

  Flag,

  AlertTriangle,
  Users,

  Clock,
  Shield,
  Trash2,

  X,
  Send,

} from 'lucide-react';
import {
  collection,
  getDocs,
  query,
  limit,
  doc,
  updateDoc,
  deleteDoc,
  Timestamp,
  getDoc,
  setDoc,
  addDoc
} from 'firebase/firestore';
import { firestore } from '../../../firebase/config';
import { logAdminAction } from '../../../utils/adminAuth';
import { useAuth } from '../../../hooks/useAuth';

interface ChatMessage {
  id: string;
  chatId: string;
  senderId: string;
  receiverId: string;
  senderName?: string;
  receiverName?: string;
  text: string;
  imageUrl?: string;
  createdAt: any;
  read: boolean;
  flagged?: boolean;
  flaggedReason?: string;
  containsRiskyContent?: boolean;
  riskFactors?: string[];
}

interface ChatConversation {
  id: string;
  participants: string[];
  participantNames: string[];
  lastMessage: string;
  lastMessageTime: any;
  messageCount: number;
  flaggedMessages: number;
  isActive: boolean;
}

const AdminChat: React.FC = () => {
  const { userProfile } = useAuth();
  const [conversations, setConversations] = useState<ChatConversation[]>([]);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<ChatConversation | null>(null);
  const [loading, setLoading] = useState(true);
  const [messagesLoading, setMessagesLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [showFlaggedOnly, setShowFlaggedOnly] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [allUsers, setAllUsers] = useState<any[]>([]);
  const [showUserList, setShowUserList] = useState(false);
  const [newMessage, setNewMessage] = useState('');
  const [sendingMessage, setSendingMessage] = useState(false);

  // Risk detection patterns
  const riskPatterns = {
    paymentMethods: /\b(venmo|paypal|cashapp|zelle|apple\s*pay|google\s*pay|bitcoin|crypto)\b/i,
    contactInfo: /\b(\d{3}[-.]?\d{3}[-.]?\d{4}|\w+@\w+\.\w+)\b/i,
    urls: /https?:\/\/[^\s]+/i,
    offPlatform: /\b(meet\s*up|outside|off\s*campus|my\s*place|your\s*place)\b/i
  };

  useEffect(() => {
    fetchConversations();
    fetchAllUsers();
  }, []);

  const fetchAllUsers = async () => {
    try {
      // Fetch all users from Firebase
      const usersQuery = query(collection(firestore, 'users'), limit(100));
      const usersSnapshot = await getDocs(usersQuery);

      const users = usersSnapshot.docs.map(doc => ({
        id: doc.id,
        uid: doc.id,
        ...doc.data()
      }));

      setAllUsers(users);
    } catch (err) {
      console.error('Error fetching users:', err);
    }
  };

  const startChatWithUser = async (userId: string, userName: string) => {
    try {
      if (!userProfile) return;

      // Create chat ID from sorted user IDs
      const chatId = [userProfile.uid, userId].sort().join('_');

      // Check if chat already exists
      const chatRef = doc(firestore, 'chats', chatId);
      const chatDoc = await getDoc(chatRef);

      if (!chatDoc.exists()) {
        // Create new chat
        await setDoc(chatRef, {
          participants: [userProfile.uid, userId],
          participantNames: [userProfile.name || 'Admin', userName],
          createdAt: new Date(),
          lastMessage: '',
          lastMessageTime: new Date(),
          isActive: true
        });
      }

      // Select this conversation
      const conversation: ChatConversation = {
        id: chatId,
        participants: [userProfile.uid, userId],
        participantNames: [userProfile.name || 'Admin', userName],
        lastMessage: '',
        lastMessageTime: new Date(),
        messageCount: 0,
        flaggedMessages: 0,
        isActive: true
      };

      setSelectedConversation(conversation);
      setShowUserList(false);
      await fetchMessages(chatId);
    } catch (err) {
      console.error('Error starting chat:', err);
    }
  };

  const sendMessage = async () => {
    if (!newMessage.trim() || !selectedConversation || !userProfile || sendingMessage) return;

    try {
      setSendingMessage(true);

      const messageData = {
        senderId: userProfile.uid,
        receiverId: selectedConversation.participants.find(p => p !== userProfile.uid),
        senderName: userProfile.name || 'Admin',
        receiverName: selectedConversation.participantNames.find(name => name !== (userProfile.name || 'Admin')),
        text: newMessage,
        createdAt: new Date(),
        read: false,
        flagged: false
      };

      // Add message to subcollection
      await addDoc(collection(firestore, 'chats', selectedConversation.id, 'messages'), messageData);

      // Update chat's last message
      await updateDoc(doc(firestore, 'chats', selectedConversation.id), {
        lastMessage: newMessage,
        lastMessageTime: new Date()
      });

      setNewMessage('');
      await fetchMessages(selectedConversation.id);
    } catch (err) {
      console.error('Error sending message:', err);
    } finally {
      setSendingMessage(false);
    }
  };

  const fetchConversations = async () => {
    try {
      setLoading(true);
      setError(null);

      // First check if chats collection exists by trying to get any document
      const chatsRef = collection(firestore, 'chats');
      const chatsSnapshot = await getDocs(query(chatsRef, limit(1)));

      if (chatsSnapshot.empty) {
        // No chats exist yet, create some sample data for demonstration
        console.log('No chats found, showing empty state');
        setConversations([]);
        setError(null); // Don't show error for empty state
        return;
      }

      // Get all chat conversations
      const allChatsQuery = query(
        collection(firestore, 'chats'),
        limit(100)
      );

      const allChatsSnapshot = await getDocs(allChatsQuery);
      const chatData: ChatConversation[] = [];

      for (const chatDoc of allChatsSnapshot.docs) {
        const chat = chatDoc.data();

        try {
          // Get message count for this chat
          const messagesQuery = query(
            collection(firestore, 'chats', chatDoc.id, 'messages')
          );

          const messagesSnapshot = await getDocs(messagesQuery);
          const messages = messagesSnapshot.docs.map(doc => doc.data());

          // Count flagged messages
          const flaggedCount = messages.filter((msg: any) =>
            msg.flagged ||
            msg.containsRiskyContent ||
            detectRiskyContent(msg.text || '').length > 0
          ).length;

          chatData.push({
            id: chatDoc.id,
            participants: chat.participants || [],
            participantNames: chat.participantNames || ['Unknown User', 'Unknown User'],
            lastMessage: chat.lastMessage || 'No messages yet',
            lastMessageTime: chat.lastMessageTime || chat.createdAt || new Date(),
            messageCount: messages.length,
            flaggedMessages: flaggedCount,
            isActive: chat.isActive !== false
          });
        } catch (msgErr) {
          console.warn('Error fetching messages for chat:', chatDoc.id, msgErr);
          // Still add the chat even if we can't get message details
          chatData.push({
            id: chatDoc.id,
            participants: chat.participants || [],
            participantNames: chat.participantNames || ['Unknown User', 'Unknown User'],
            lastMessage: chat.lastMessage || 'No messages yet',
            lastMessageTime: chat.lastMessageTime || chat.createdAt || new Date(),
            messageCount: 0,
            flaggedMessages: 0,
            isActive: chat.isActive !== false
          });
        }
      }

      setConversations(chatData);
      setError(null);
    } catch (err) {
      console.error('Error fetching conversations:', err);
      // Don't show error for permission issues or missing collections
      setConversations([]);
      setError(null);
    } finally {
      setLoading(false);
    }
  };

  const fetchMessages = async (chatId: string) => {
    try {
      setMessagesLoading(true);

      // Check if messages subcollection exists
      const messagesRef = collection(firestore, 'chats', chatId, 'messages');
      const messagesSnapshot = await getDocs(messagesRef);

      if (messagesSnapshot.empty) {
        setMessages([]);
        return;
      }

      const messageData: ChatMessage[] = messagesSnapshot.docs.map(doc => {
        const data = doc.data();

        // Ensure text is always a string
        let messageText = '';
        if (data.text) {
          if (typeof data.text === 'string') {
            messageText = data.text;
          } else if (typeof data.text === 'object') {
            // If text is an object, try to extract meaningful content
            messageText = JSON.stringify(data.text);
          } else {
            messageText = String(data.text);
          }
        }

        const riskFactors = detectRiskyContent(messageText);

        return {
          id: doc.id,
          chatId,
          senderId: String(data.senderId || ''),
          receiverId: String(data.receiverId || ''),
          senderName: String(data.senderName || 'Unknown User'),
          receiverName: String(data.receiverName || 'Unknown User'),
          text: messageText,
          imageUrl: data.imageUrl ? String(data.imageUrl) : undefined,
          createdAt: data.createdAt || new Date(),
          read: Boolean(data.read),
          flagged: Boolean(data.flagged),
          flaggedReason: data.flaggedReason ? String(data.flaggedReason) : undefined,
          containsRiskyContent: riskFactors.length > 0,
          riskFactors
        };
      });

      // Sort by creation time (oldest first)
      messageData.sort((a, b) => {
        const aTime = a.createdAt?.toDate ? a.createdAt.toDate() : new Date(a.createdAt);
        const bTime = b.createdAt?.toDate ? b.createdAt.toDate() : new Date(b.createdAt);
        return aTime.getTime() - bTime.getTime();
      });

      setMessages(messageData);
    } catch (err) {
      console.error('Error fetching messages:', err);
      setMessages([]);
    } finally {
      setMessagesLoading(false);
    }
  };

  const detectRiskyContent = (text: string): string[] => {
    const risks: string[] = [];

    if (riskPatterns.paymentMethods.test(text)) {
      risks.push('Payment Method');
    }
    if (riskPatterns.contactInfo.test(text)) {
      risks.push('Contact Info');
    }
    if (riskPatterns.urls.test(text)) {
      risks.push('External URL');
    }
    if (riskPatterns.offPlatform.test(text)) {
      risks.push('Off-Platform Meeting');
    }

    return risks;
  };

  const handleConversationSelect = async (conversation: ChatConversation) => {
    setSelectedConversation(conversation);
    await fetchMessages(conversation.id);
  };

  const handleMessageAction = async (messageId: string, chatId: string, action: string) => {
    try {
      if (userProfile) {
        await logAdminAction(userProfile, `message_${action}`, { messageId, chatId });
      }

      const messageRef = doc(firestore, 'chats', chatId, 'messages', messageId);

      switch (action) {
        case 'flag':
          await updateDoc(messageRef, {
            flagged: true,
            flaggedReason: 'Admin flagged',
            flaggedAt: Timestamp.now()
          });
          break;
        case 'unflag':
          await updateDoc(messageRef, {
            flagged: false,
            flaggedReason: null,
            flaggedAt: null
          });
          break;
        case 'delete':
          if (confirm('Are you sure you want to delete this message?')) {
            await deleteDoc(messageRef);
          }
          break;
      }

      // Refresh messages
      if (selectedConversation) {
        await fetchMessages(selectedConversation.id);
      }
    } catch (err) {
      console.error(`Error performing ${action} on message:`, err);
    }
  };

  const filteredConversations = conversations.filter(conv => {
    const matchesSearch =
      conv.participantNames.some(name =>
        name.toLowerCase().includes(searchTerm.toLowerCase())
      ) ||
      conv.lastMessage.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesFlagged = !showFlaggedOnly || conv.flaggedMessages > 0;

    return matchesSearch && matchesFlagged;
  });

  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'N/A';

    try {
      let date: Date;

      if (timestamp.toDate && typeof timestamp.toDate === 'function') {
        date = timestamp.toDate();
      } else if (timestamp.seconds && typeof timestamp.seconds === 'number') {
        date = new Date(timestamp.seconds * 1000);
      } else if (timestamp._seconds && typeof timestamp._seconds === 'number') {
        date = new Date(timestamp._seconds * 1000);
      } else {
        date = new Date(timestamp);
      }

      if (isNaN(date.getTime())) {
        return 'N/A';
      }

      return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    } catch (error) {
      console.warn('Error formatting date:', error);
      return 'N/A';
    }
  };

  const getRiskColor = (riskFactors: string[]) => {
    if (riskFactors.length === 0) return '';
    if (riskFactors.length >= 2) return 'border-l-4 border-red-500 bg-red-50 dark:bg-red-900/20';
    return 'border-l-4 border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20';
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600 dark:text-gray-400">Loading conversations...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex">
            <AlertTriangle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                Error Loading Conversations
              </h3>
              <p className="mt-1 text-sm text-red-700 dark:text-red-300">{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Chat & Messaging</h2>
          <p className="text-gray-600 dark:text-gray-400">
            Monitor conversations and moderate content ({conversations.length} conversations)
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowUserList(true)}
            className="inline-flex items-center px-4 py-2 border border-blue-300 text-sm font-medium rounded-md text-blue-700 bg-blue-50 hover:bg-blue-100"
          >
            <MessageSquare className="h-4 w-4 mr-2" />
            Start New Chat
          </button>
          <button
            onClick={() => setShowFlaggedOnly(!showFlaggedOnly)}
            className={`inline-flex items-center px-4 py-2 border text-sm font-medium rounded-md ${
              showFlaggedOnly
                ? 'border-red-300 text-red-700 bg-red-50 hover:bg-red-100'
                : 'border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600'
            }`}
          >
            <Flag className="h-4 w-4 mr-2" />
            {showFlaggedOnly ? 'Show All' : 'Flagged Only'}
          </button>
        </div>
      </div>

      {/* Search */}
      <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700 p-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            id="admin-chat-search"
            name="chatSearch"
            type="text"
            placeholder="Search conversations by participant or message content..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Conversations List */}
        <div className="lg:col-span-1">
          <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">
                Conversations ({filteredConversations.length})
              </h3>

              {filteredConversations.length === 0 ? (
                <div className="text-center py-8">
                  <MessageSquare className="mx-auto h-8 w-8 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No conversations found</h3>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    {searchTerm ? 'Try adjusting your search terms.' : 'No conversations match the current filters.'}
                  </p>
                </div>
              ) : (
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {filteredConversations.map((conversation) => (
                    <div
                      key={conversation.id}
                      className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                        selectedConversation?.id === conversation.id
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                          : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                      }`}
                      onClick={() => handleConversationSelect(conversation)}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <Users className="h-4 w-4 text-gray-400" />
                          <span className="text-sm font-medium text-gray-900 dark:text-white">
                            {Array.isArray(conversation.participantNames)
                              ? conversation.participantNames.map(name => String(name)).join(' & ')
                              : String(conversation.participantNames || 'Unknown')
                            }
                          </span>
                        </div>
                        {conversation.flaggedMessages > 0 && (
                          <div className="flex items-center">
                            <Flag className="h-3 w-3 text-red-500 mr-1" />
                            <span className="text-xs text-red-600 dark:text-red-400">
                              {conversation.flaggedMessages}
                            </span>
                          </div>
                        )}
                      </div>

                      <p className="text-sm text-gray-600 dark:text-gray-400 truncate mb-2">
                        {typeof conversation.lastMessage === 'string'
                          ? conversation.lastMessage
                          : String(conversation.lastMessage || 'No messages')
                        }
                      </p>

                      <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                        <div className="flex items-center">
                          <MessageSquare className="h-3 w-3 mr-1" />
                          {conversation.messageCount} messages
                        </div>
                        <div className="flex items-center">
                          <Clock className="h-3 w-3 mr-1" />
                          {formatDate(conversation.lastMessageTime)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Messages Panel */}
        <div className="lg:col-span-2">
          <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="px-4 py-5 sm:p-6">
              {selectedConversation ? (
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                      Messages: {selectedConversation.participantNames.join(' & ')}
                    </h3>
                    <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                      <MessageSquare className="h-4 w-4" />
                      <span>{messages.length} messages</span>
                      {selectedConversation.flaggedMessages > 0 && (
                        <>
                          <Flag className="h-4 w-4 text-red-500 ml-2" />
                          <span className="text-red-600 dark:text-red-400">
                            {selectedConversation.flaggedMessages} flagged
                          </span>
                        </>
                      )}
                    </div>
                  </div>

                  {messagesLoading ? (
                    <div className="flex items-center justify-center h-32">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                      <span className="ml-3 text-gray-600 dark:text-gray-400">Loading messages...</span>
                    </div>
                  ) : messages.length === 0 ? (
                    <div className="text-center py-8">
                      <MessageSquare className="mx-auto h-8 w-8 text-gray-400" />
                      <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No messages</h3>
                      <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        This conversation has no messages yet.
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4 max-h-96 overflow-y-auto">
                      {messages.map((message) => (
                        <div
                          key={message.id}
                          className={`p-4 rounded-lg ${getRiskColor(message.riskFactors || [])} ${
                            message.flagged ? 'ring-2 ring-red-500' : ''
                          }`}
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center space-x-2 mb-2">
                                <span className="text-sm font-medium text-gray-900 dark:text-white">
                                  {typeof message.senderName === 'string'
                                    ? message.senderName
                                    : String(message.senderName || 'Unknown User')
                                  }
                                </span>
                                <span className="text-xs text-gray-500 dark:text-gray-400">
                                  {formatDate(message.createdAt)}
                                </span>
                                {message.flagged && (
                                  <span className="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300">
                                    <Flag className="h-3 w-3 mr-1" />
                                    Flagged
                                  </span>
                                )}
                              </div>

                              <div className="text-sm text-gray-900 dark:text-white mb-2">
                                {typeof message.text === 'string' ? message.text : String(message.text || '')}
                              </div>

                              {message.imageUrl && (
                                <div className="mb-2">
                                  <img
                                    src={message.imageUrl}
                                    alt="Message attachment"
                                    className="max-w-xs rounded-lg"
                                  />
                                </div>
                              )}

                              {message.riskFactors && message.riskFactors.length > 0 && (
                                <div className="flex flex-wrap gap-1 mb-2">
                                  {message.riskFactors.map((risk, index) => (
                                    <span
                                      key={index}
                                      className="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300"
                                    >
                                      <AlertTriangle className="h-3 w-3 mr-1" />
                                      {typeof risk === 'string' ? risk : String(risk)}
                                    </span>
                                  ))}
                                </div>
                              )}
                            </div>

                            <div className="flex items-center space-x-2 ml-4">
                              <button
                                onClick={() => handleMessageAction(message.id, message.chatId, message.flagged ? 'unflag' : 'flag')}
                                className={`p-1 rounded ${
                                  message.flagged
                                    ? 'text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300'
                                    : 'text-orange-600 hover:text-orange-800 dark:text-orange-400 dark:hover:text-orange-300'
                                }`}
                                title={message.flagged ? 'Unflag Message' : 'Flag Message'}
                              >
                                {message.flagged ? <Shield className="h-4 w-4" /> : <Flag className="h-4 w-4" />}
                              </button>
                              <button
                                onClick={() => handleMessageAction(message.id, message.chatId, 'delete')}
                                className="p-1 rounded text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                                title="Delete Message"
                              >
                                <Trash2 className="h-4 w-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-12">
                  <MessageSquare className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                    Select a Conversation
                  </h3>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Choose a conversation from the list to view and moderate messages.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* User List Modal */}
      {showUserList && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
            <div className="mt-3">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  Start New Chat
                </h3>
                <button
                  onClick={() => setShowUserList(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>

              <div className="mb-4">
                <input
                  type="text"
                  placeholder="Search users..."
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>

              <div className="max-h-64 overflow-y-auto">
                {allUsers
                  .filter(user =>
                    user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    user.email?.toLowerCase().includes(searchTerm.toLowerCase())
                  )
                  .map((user) => (
                    <div
                      key={user.uid}
                      className="flex items-center justify-between p-3 hover:bg-gray-50 dark:hover:bg-gray-700 rounded cursor-pointer"
                      onClick={() => startChatWithUser(user.uid, user.name || user.email)}
                    >
                      <div className="flex items-center space-x-3">
                        <img
                          src={user.profilePictureURL || '/placeholder-avatar.jpg'}
                          alt={user.name}
                          className="h-8 w-8 rounded-full bg-gray-300"
                        />
                        <div>
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {user.name || 'Unknown User'}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            {user.email}
                          </p>
                        </div>
                      </div>
                      <MessageSquare className="h-4 w-4 text-blue-500" />
                    </div>
                  ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Chat Interface */}
      {selectedConversation && (
        <div className="fixed bottom-4 right-4 w-80 h-96 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-40">
          <div className="flex flex-col h-full">
            {/* Chat Header */}
            <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center space-x-2">
                <MessageSquare className="h-4 w-4 text-blue-500" />
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {selectedConversation.participantNames.find(name => name !== (userProfile?.name || 'Admin'))}
                </span>
              </div>
              <button
                onClick={() => setSelectedConversation(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-4 w-4" />
              </button>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-3 space-y-2">
              {messagesLoading ? (
                <div className="flex items-center justify-center h-full">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                </div>
              ) : messages.length > 0 ? (
                messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.senderId === userProfile?.uid ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-xs px-3 py-2 rounded-lg text-sm ${
                        message.senderId === userProfile?.uid
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white'
                      }`}
                    >
                      <p>{typeof message.text === 'string' ? message.text : String(message.text || '')}</p>
                      <p className="text-xs opacity-75 mt-1">
                        {formatDate(message.createdAt)}
                      </p>
                    </div>
                  </div>
                ))
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500 dark:text-gray-400">
                  <p className="text-sm">No messages yet</p>
                </div>
              )}
            </div>

            {/* Message Input */}
            <div className="p-3 border-t border-gray-200 dark:border-gray-700">
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                  placeholder="Type a message..."
                  className="flex-1 px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  disabled={sendingMessage}
                />
                <button
                  onClick={sendMessage}
                  disabled={!newMessage.trim() || sendingMessage}
                  className="px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Send className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminChat;
