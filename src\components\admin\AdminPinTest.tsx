import React, { useState } from 'react';
import { httpsCallable } from 'firebase/functions';
import { functions } from '../../firebase/config';
import { useAuth } from '../../hooks/useAuth';

const AdminPinTest: React.FC = () => {
  const { currentUser } = useAuth();
  const [testPin, setTestPin] = useState('');
  const [setupPin, setSetupPin] = useState('');
  const [confirmPin, setConfirmPin] = useState('');
  const [adminEmail, setAdminEmail] = useState('');
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testVerifyPin = async () => {
    if (!testPin || testPin.length !== 8) {
      setResult('PIN must be exactly 8 digits');
      return;
    }

    setLoading(true);
    try {
      const verifyPin = httpsCallable(functions, 'verifyAdminPin');
      const response = await verifyPin({ pin: testPin });
      setResult(`✅ PIN verification successful: ${JSON.stringify(response.data)}`);
    } catch (error: any) {
      setResult(`❌ PIN verification failed: ${error.code} - ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testSetupPin = async () => {
    if (!setupPin || setupPin.length !== 8) {
      setResult('Setup PIN must be exactly 8 digits');
      return;
    }

    if (setupPin !== confirmPin) {
      setResult('PINs do not match');
      return;
    }

    setLoading(true);
    try {
      const setAdminPin = httpsCallable(functions, 'setAdminPin');
      const response = await setAdminPin({ pin: setupPin });
      setResult(`✅ PIN setup successful: ${JSON.stringify(response.data)}`);
    } catch (error: any) {
      setResult(`❌ PIN setup failed: ${error.code} - ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const clearSession = () => {
    sessionStorage.removeItem('adminPinVerified');
    sessionStorage.removeItem('adminPinTime');
    setResult('✅ Session storage cleared');
  };

  const checkSessionStatus = () => {
    const pinVerified = sessionStorage.getItem('adminPinVerified');
    const pinTime = sessionStorage.getItem('adminPinTime');

    if (pinVerified && pinTime) {
      const timeDiff = Date.now() - parseInt(pinTime);
      const minutesAgo = Math.floor(timeDiff / (1000 * 60));
      setResult(`📋 Session Status: PIN verified ${minutesAgo} minutes ago`);
    } else {
      setResult('📋 Session Status: No PIN verification found');
    }
  };

  const fixAdminUser = async () => {
    if (!adminEmail) {
      setResult('Please enter an email address');
      return;
    }

    setLoading(true);
    try {
      const fixAdmin = httpsCallable(functions, 'fixAdminUser');
      const response = await fixAdmin({ email: adminEmail });
      setResult(`✅ Admin user setup successful: ${JSON.stringify(response.data)}`);
    } catch (error: any) {
      setResult(`❌ Admin setup failed: ${error.code} - ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold mb-6 text-gray-900 dark:text-white">
        Admin PIN Test Panel
      </h2>

      <div className="space-y-6">
        {/* Current User Info */}
        <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
          <h3 className="font-semibold mb-2">Current User:</h3>
          <p>Email: {currentUser?.email}</p>
          <p>UID: {currentUser?.uid}</p>
        </div>

        {/* Test PIN Verification */}
        <div className="border border-gray-200 dark:border-gray-600 p-4 rounded-lg">
          <h3 className="font-semibold mb-3">Test PIN Verification</h3>
          <div className="flex gap-2 mb-2">
            <input
              type="password"
              value={testPin}
              onChange={(e) => setTestPin(e.target.value.replace(/\D/g, '').slice(0, 8))}
              placeholder="Enter 8-digit PIN"
              className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
              maxLength={8}
            />
            <button
              onClick={testVerifyPin}
              disabled={loading}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              Test Verify
            </button>
          </div>
        </div>

        {/* Admin User Setup */}
        <div className="border border-red-200 dark:border-red-600 p-4 rounded-lg bg-red-50 dark:bg-red-900/20">
          <h3 className="font-semibold mb-3 text-red-800 dark:text-red-300">Setup Admin User</h3>
          <div className="space-y-2">
            <input
              type="email"
              value={adminEmail}
              onChange={(e) => setAdminEmail(e.target.value)}
              placeholder="Enter admin email address"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
            />
            <button
              onClick={fixAdminUser}
              disabled={loading}
              className="w-full px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
            >
              Fix Admin User
            </button>
          </div>
          <p className="text-xs text-red-600 dark:text-red-400 mt-2">
            ⚠️ Use this to set up admin permissions for an existing user account
          </p>
        </div>

        {/* Setup PIN */}
        <div className="border border-gray-200 dark:border-gray-600 p-4 rounded-lg">
          <h3 className="font-semibold mb-3">Setup New PIN</h3>
          <div className="space-y-2">
            <input
              type="password"
              value={setupPin}
              onChange={(e) => setSetupPin(e.target.value.replace(/\D/g, '').slice(0, 8))}
              placeholder="Enter new 8-digit PIN"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
              maxLength={8}
            />
            <input
              type="password"
              value={confirmPin}
              onChange={(e) => setConfirmPin(e.target.value.replace(/\D/g, '').slice(0, 8))}
              placeholder="Confirm PIN"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
              maxLength={8}
            />
            <button
              onClick={testSetupPin}
              disabled={loading}
              className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
            >
              Setup PIN
            </button>
          </div>
        </div>

        {/* Session Management */}
        <div className="border border-gray-200 dark:border-gray-600 p-4 rounded-lg">
          <h3 className="font-semibold mb-3">Session Management</h3>
          <div className="flex gap-2">
            <button
              onClick={checkSessionStatus}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
            >
              Check Session
            </button>
            <button
              onClick={clearSession}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
            >
              Clear Session
            </button>
          </div>
        </div>

        {/* Result Display */}
        {result && (
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <h3 className="font-semibold mb-2">Result:</h3>
            <pre className="text-sm whitespace-pre-wrap">{result}</pre>
          </div>
        )}

        {/* Instructions */}
        <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
          <h3 className="font-semibold mb-2">Instructions:</h3>
          <ol className="list-decimal list-inside space-y-1 text-sm">
            <li><strong>First time setup:</strong> Use "Fix Admin User" with your email to set admin permissions</li>
            <li><strong>Login:</strong> Sign in with your email/password at the login page</li>
            <li><strong>Setup PIN:</strong> Create an 8-digit PIN for admin access</li>
            <li><strong>Test PIN:</strong> Verify your PIN works correctly</li>
            <li><strong>Clear Session:</strong> Force re-authentication to test the flow</li>
            <li><strong>Check Session:</strong> See current authentication status</li>
          </ol>
          <div className="mt-3 p-2 bg-yellow-100 dark:bg-yellow-900/20 rounded text-xs">
            <strong>Note:</strong> If you get "invalid-credential" errors, make sure you've created a user account first and used "Fix Admin User" to grant admin permissions.
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminPinTest;
