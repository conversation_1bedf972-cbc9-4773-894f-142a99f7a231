// Simple test script for Stripe webhook functionality
const crypto = require('crypto');

// Test configuration
const WEBHOOK_URL = 'https://us-central1-h1c1-798a8.cloudfunctions.net/stripeWebhook';
const WEBHOOK_SECRET = 'whsec_Ggd0wEt8z8NzRV5kyEyvXH2iB1xrWSZq';

// Mock Stripe events for testing
const mockEvents = {
  checkoutSessionCompleted: {
    id: 'evt_test_checkout_completed',
    object: 'event',
    api_version: '2025-05-28.basil',
    created: Math.floor(Date.now() / 1000),
    data: {
      object: {
        id: 'cs_test_checkout_session_123',
        object: 'checkout.session',
        amount_total: 2599, // $25.99 in cents
        currency: 'usd',
        customer: null,
        customer_email: '<EMAIL>',
        metadata: {
          orderId: 'test-order-webhook-123',
          listingId: 'test-listing-webhook-456',
          buyerId: 'test-buyer-webhook-789',
          sellerId: 'test-seller-webhook-101',
          cashbackAmount: '0.52'
        },
        payment_intent: 'pi_test_payment_intent_123',
        payment_status: 'paid',
        status: 'complete'
      }
    },
    livemode: false,
    pending_webhooks: 1,
    request: {
      id: 'req_test_request_123',
      idempotency_key: null
    },
    type: 'checkout.session.completed'
  },

  paymentIntentSucceeded: {
    id: 'evt_test_payment_succeeded',
    object: 'event',
    api_version: '2025-05-28.basil',
    created: Math.floor(Date.now() / 1000),
    data: {
      object: {
        id: 'pi_test_payment_intent_123',
        object: 'payment_intent',
        amount: 2599,
        currency: 'usd',
        customer: null,
        metadata: {
          orderId: 'test-order-webhook-123',
          listingId: 'test-listing-webhook-456',
          buyerId: 'test-buyer-webhook-789',
          sellerId: 'test-seller-webhook-101',
          cashbackAmount: '0.52'
        },
        status: 'succeeded'
      }
    },
    livemode: false,
    pending_webhooks: 1,
    request: {
      id: 'req_test_request_456',
      idempotency_key: null
    },
    type: 'payment_intent.succeeded'
  },

  accountUpdated: {
    id: 'evt_test_account_updated',
    object: 'event',
    api_version: '2025-05-28.basil',
    created: Math.floor(Date.now() / 1000),
    data: {
      object: {
        id: 'acct_test_account_123',
        object: 'account',
        charges_enabled: true,
        payouts_enabled: true,
        details_submitted: true,
        metadata: {
          userId: 'test-seller-webhook-101'
        }
      }
    },
    livemode: false,
    pending_webhooks: 1,
    request: {
      id: 'req_test_request_789',
      idempotency_key: null
    },
    type: 'account.updated'
  }
};

// Function to create a valid Stripe signature
function createStripeSignature(payload, secret) {
  const timestamp = Math.floor(Date.now() / 1000);
  const payloadString = JSON.stringify(payload);
  const signedPayload = `${timestamp}.${payloadString}`;
  const signature = crypto
    .createHmac('sha256', secret)
    .update(signedPayload, 'utf8')
    .digest('hex');
  
  return `t=${timestamp},v1=${signature}`;
}

// Function to test a webhook event
async function testWebhookEvent(eventName, event) {
  try {
    console.log(`\n🧪 Testing ${eventName}...`);
    console.log(`📡 Event ID: ${event.id}`);
    console.log(`📋 Event Type: ${event.type}`);
    
    const payload = JSON.stringify(event);
    const signature = createStripeSignature(event, WEBHOOK_SECRET);
    
    console.log(`🔐 Generated signature: ${signature.substring(0, 50)}...`);
    
    const response = await fetch(WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Stripe-Signature': signature,
        'User-Agent': 'Stripe/1.0 (+https://stripe.com/docs/webhooks)'
      },
      body: payload
    });
    
    const responseText = await response.text();
    
    console.log(`📊 Response Status: ${response.status}`);
    console.log(`📄 Response Headers:`, Object.fromEntries(response.headers.entries()));
    console.log(`📝 Response Body: ${responseText}`);
    
    if (response.status === 200) {
      console.log(`✅ ${eventName} webhook test PASSED!`);
      return { success: true, status: response.status, body: responseText };
    } else {
      console.log(`❌ ${eventName} webhook test FAILED!`);
      return { success: false, status: response.status, body: responseText };
    }
    
  } catch (error) {
    console.error(`❌ Error testing ${eventName}:`, error.message);
    return { success: false, error: error.message };
  }
}

// Function to test webhook endpoint availability
async function testWebhookEndpoint() {
  try {
    console.log('🔗 Testing webhook endpoint availability...');
    
    // Test with GET request (should return 405)
    const getResponse = await fetch(WEBHOOK_URL, {
      method: 'GET'
    });
    
    console.log(`📡 GET request status: ${getResponse.status}`);
    
    if (getResponse.status === 405) {
      console.log('✅ Webhook endpoint is responding correctly (405 for GET)');
      return true;
    } else {
      console.log('⚠️ Unexpected response from webhook endpoint');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Error testing webhook endpoint:', error.message);
    return false;
  }
}

// Function to test invalid signature
async function testInvalidSignature() {
  try {
    console.log('\n🔒 Testing invalid signature handling...');
    
    const event = mockEvents.checkoutSessionCompleted;
    const payload = JSON.stringify(event);
    const invalidSignature = 't=1234567890,v1=invalid_signature_here';
    
    const response = await fetch(WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Stripe-Signature': invalidSignature
      },
      body: payload
    });
    
    console.log(`📊 Invalid signature response status: ${response.status}`);
    
    if (response.status === 400) {
      console.log('✅ Invalid signature correctly rejected');
      return true;
    } else {
      console.log('❌ Invalid signature not properly handled');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Error testing invalid signature:', error.message);
    return false;
  }
}

// Function to test missing signature
async function testMissingSignature() {
  try {
    console.log('\n🚫 Testing missing signature handling...');
    
    const event = mockEvents.checkoutSessionCompleted;
    const payload = JSON.stringify(event);
    
    const response = await fetch(WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
        // No Stripe-Signature header
      },
      body: payload
    });
    
    console.log(`📊 Missing signature response status: ${response.status}`);
    
    if (response.status === 400) {
      console.log('✅ Missing signature correctly rejected');
      return true;
    } else {
      console.log('❌ Missing signature not properly handled');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Error testing missing signature:', error.message);
    return false;
  }
}

// Main test function
async function runWebhookTests() {
  console.log('🚀 Starting Stripe Webhook Tests');
  console.log('='.repeat(50));
  console.log(`🔗 Webhook URL: ${WEBHOOK_URL}`);
  console.log(`🔐 Webhook Secret: ${WEBHOOK_SECRET.substring(0, 20)}...`);
  console.log('='.repeat(50));
  
  const results = {
    endpointAvailable: false,
    invalidSignature: false,
    missingSignature: false,
    checkoutSessionCompleted: false,
    paymentIntentSucceeded: false,
    accountUpdated: false
  };
  
  try {
    // Test 1: Endpoint availability
    results.endpointAvailable = await testWebhookEndpoint();
    
    // Test 2: Invalid signature handling
    results.invalidSignature = await testInvalidSignature();
    
    // Test 3: Missing signature handling
    results.missingSignature = await testMissingSignature();
    
    // Test 4: Valid webhook events
    const checkoutResult = await testWebhookEvent('Checkout Session Completed', mockEvents.checkoutSessionCompleted);
    results.checkoutSessionCompleted = checkoutResult.success;
    
    const paymentResult = await testWebhookEvent('Payment Intent Succeeded', mockEvents.paymentIntentSucceeded);
    results.paymentIntentSucceeded = paymentResult.success;
    
    const accountResult = await testWebhookEvent('Account Updated', mockEvents.accountUpdated);
    results.accountUpdated = accountResult.success;
    
    // Summary
    console.log('\n' + '='.repeat(50));
    console.log('📊 TEST RESULTS SUMMARY');
    console.log('='.repeat(50));
    
    Object.entries(results).forEach(([test, passed]) => {
      const status = passed ? '✅ PASSED' : '❌ FAILED';
      const testName = test.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
      console.log(`${status} - ${testName}`);
    });
    
    const totalTests = Object.keys(results).length;
    const passedTests = Object.values(results).filter(Boolean).length;
    
    console.log('\n' + '='.repeat(50));
    console.log(`📈 Overall Results: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
      console.log('🎉 ALL TESTS PASSED! Stripe webhook is working correctly.');
    } else {
      console.log('⚠️ Some tests failed. Please check the logs above for details.');
    }
    
    console.log('\n💡 Next Steps:');
    console.log('1. Configure this webhook URL in your Stripe Dashboard');
    console.log('2. Test with real Stripe events using Stripe CLI or test payments');
    console.log('3. Monitor Firebase Functions logs for webhook processing');
    console.log('4. Verify order creation and notifications in Firestore');
    
  } catch (error) {
    console.error('❌ Test suite failed:', error);
  }
}

// Run the tests
runWebhookTests();
