# Commission Structure Test Cases

## New Commission Structure Implementation

### Price Range Rules:
- **$1-$5**: Flat $0.50 fee (regardless of category)
- **$5-$10**: 8% for textbooks/course materials, 10% for other categories  
- **$10+**: 8% for textbooks/course materials, 10% for other categories

### Test Cases:

#### 1. Low Price Range ($1-$5)
- **$3 Textbook**: Platform fee = $0.50, <PERSON><PERSON> receives = $2.50
- **$5 Electronics**: Platform fee = $0.50, <PERSON><PERSON> receives = $4.50
- **$1 Notebook**: Platform fee = $0.50, Se<PERSON> receives = $0.50

#### 2. Medium Price Range ($5-$10)
- **$8 Textbook**: Platform fee = $0.64 (8%), Seller receives = $7.36
- **$10 Electronics**: Platform fee = $1.00 (10%), Seller receives = $9.00
- **$7 Course Materials**: Platform fee = $0.56 (8%), Seller receives = $6.44

#### 3. High Price Range ($10+)
- **$20 Textbook**: Platform fee = $1.60 (8%), Seller receives = $18.40
- **$50 Electronics**: Platform fee = $5.00 (10%), Seller receives = $45.00
- **$100 Course Materials**: Platform fee = $8.00 (8%), Seller receives = $92.00

### Categories:
- **Textbook Categories**: 'textbooks', 'course-materials', 'books'
- **Other Categories**: All other categories (electronics, clothing, etc.)

### Implementation Notes:
- Stripe fees are included within the platform fee (not additional)
- Minimum listing price: $1
- Commission calculated on item price only (not including shipping)
- Wallet credits reduce Stripe charge but seller still gets full amount minus platform fee

### Test Scenarios with Wallet:

#### Scenario 1: $3 Textbook with $2 Wallet Credit
- Item Price: $3.00
- Platform Fee: $0.50 (flat fee)
- Seller Receives: $2.50
- Buyer Pays via Stripe: $1.00 ($3 - $2 wallet)
- Wallet Deducted: $2.00

#### Scenario 2: $20 Electronics with $5 Wallet Credit  
- Item Price: $20.00
- Platform Fee: $2.00 (10%)
- Seller Receives: $18.00
- Buyer Pays via Stripe: $15.00 ($20 - $5 wallet)
- Wallet Deducted: $5.00

#### Scenario 3: $8 Course Materials with $10 Wallet Credit
- Item Price: $8.00
- Platform Fee: $0.64 (8%)
- Seller Receives: $7.36
- Buyer Pays via Stripe: $0.00 (fully covered by wallet)
- Wallet Deducted: $8.00

### Shipping Integration:
- Shipping costs are separate from commission calculation
- Commission only applies to item price
- Shipping rates calculated via Shippo API
- Shipping labels generated automatically for mail orders

### Escrow Flow:
1. **Payment Success**: Order status = 'in_progress', secret code generated
2. **Shipping Orders**: Label generated, tracking starts
3. **In-Person Orders**: Seller marks as delivered manually
4. **Delivery Confirmed**: 3-day return window starts
5. **Secret Code Entry**: Funds released immediately
6. **Auto-Release**: After 3 days if no secret code entered
7. **Returns**: Can be requested within 3-day window

### Security Features:
- Secret codes are 6-digit random numbers
- Single-use protection (status checks)
- User verification (only buyer can release funds)
- Order status validation
- Return window enforcement

### Database Structure:
```typescript
orders/{orderId} = {
  // Existing fields...
  secretCode: "123456",
  deliveryType: "shipping" | "in_person",
  platformFee: number,
  sellerAmount: number,
  returnEligibleUntil: Timestamp | null,
  releasedToSeller: boolean,
  shippingTrackingNumber?: string,
  shippingLabelUrl?: string,
  deliveryConfirmedAt?: Timestamp,
  fundsReleasedAt?: Timestamp
}
```

### API Endpoints:
- `releaseEscrowWithCode(orderId, secretCode)` - Release funds with secret code
- `markDeliveryCompleted(orderId)` - Seller marks in-person delivery complete
- `requestReturn(orderId, reason)` - Buyer requests return
- `getShippingRates(listingId, address)` - Get Shippo shipping rates
- `generateShippingLabel(orderId, rateId)` - Generate shipping label
- `trackShipment(orderId)` - Get tracking updates

### Frontend Components:
- `EscrowOrderCard` - Order management with secret code entry
- Enhanced `OrderHistory` - Shows new order statuses
- Shipping rate selection in checkout
- Return request interface
- Seller delivery confirmation

This implementation provides a complete escrow system with commission-based fees, automatic shipping integration, and secure fund release mechanisms.
