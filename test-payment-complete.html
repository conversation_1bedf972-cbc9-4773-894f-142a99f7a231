<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hive Campus - Payment Flow Test</title>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-functions-compat.js"></script>
    <script src="https://js.stripe.com/v3/"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .product-card {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎓 Hive Campus - Payment Flow Test</h1>
        <p>This page tests the complete Stripe payment workflow including webhook processing.</p>

        <!-- Firebase Configuration Test -->
        <div class="test-section info">
            <h3>🔧 Firebase Configuration</h3>
            <button onclick="testFirebaseConfig()">Test Firebase Connection</button>
            <div id="firebase-status"></div>
        </div>

        <!-- Authentication Test -->
        <div class="test-section info">
            <h3>🔐 Authentication</h3>
            <button onclick="signInAnonymously()">Sign In Anonymously</button>
            <button onclick="signOut()">Sign Out</button>
            <div id="auth-status"></div>
        </div>

        <!-- Test Product -->
        <div class="test-section">
            <h3>📚 Test Product</h3>
            <div class="product-card">
                <h4>Test Textbook - Advanced Mathematics</h4>
                <p>Perfect condition textbook for testing the payment flow</p>
                <p><strong>Price: $25.99</strong></p>
                <p><strong>Seller:</strong> Test Seller</p>
                <p><strong>Cashback:</strong> 2% ($0.52)</p>
                <button onclick="createTestListing()" id="create-listing-btn">Create Test Listing</button>
                <button onclick="purchaseItem()" id="purchase-btn" disabled>Purchase with Stripe</button>
            </div>
            <div id="listing-status"></div>
        </div>

        <!-- Webhook Test -->
        <div class="test-section">
            <h3>🔗 Webhook Test</h3>
            <p><strong>Webhook URL:</strong> https://us-central1-h1c1-798a8.cloudfunctions.net/stripeWebhook</p>
            <p><strong>Secret:</strong> whsec_Ggd0wEt8z8NzRV5kyEyvXH2iB1xrWSZq</p>
            <button onclick="testWebhookEndpoint()">Test Webhook Endpoint</button>
            <div id="webhook-status"></div>
        </div>

        <!-- Order Status -->
        <div class="test-section">
            <h3>📦 Order Status</h3>
            <button onclick="checkOrderStatus()" id="check-order-btn" disabled>Check Latest Order</button>
            <div id="order-status"></div>
        </div>

        <!-- Notifications -->
        <div class="test-section">
            <h3>📧 Notifications</h3>
            <button onclick="checkNotifications()">Check Notifications</button>
            <div id="notifications-status"></div>
        </div>

        <!-- Logs -->
        <div class="test-section">
            <h3>📋 Test Logs</h3>
            <button onclick="clearLogs()">Clear Logs</button>
            <div id="logs" class="log"></div>
        </div>
    </div>

    <script>
        // Firebase configuration (Updated with correct values)
        const firebaseConfig = {
            apiKey: "AIzaSyBnF-pVKlXNdwBQ-74SEfw7NHJLLmL5e6A",
            authDomain: "h1c1-798a8.firebaseapp.com",
            projectId: "h1c1-798a8",
            storageBucket: "h1c1-798a8.firebasestorage.app",
            messagingSenderId: "1096652648176",
            appId: "1:1096652648176:web:4caac283b87d55a4ba9c35",
            measurementId: "G-6WQNVK6V4K"
        };

        // Initialize Firebase
        let app, auth, db, functions;
        try {
            app = firebase.initializeApp(firebaseConfig);
            auth = firebase.auth();
            db = firebase.firestore();
            functions = firebase.functions();

            // Set functions region
            functions = firebase.app().functions('us-central1');

            console.log('Firebase initialized successfully');
        } catch (error) {
            console.error('Firebase initialization error:', error);
        }

        // Global variables
        let currentUser = null;
        let testListingId = null;
        let latestOrderId = null;

        // Logging function
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('logs');
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(logEntry);
        }

        function clearLogs() {
            document.getElementById('logs').textContent = '';
        }

        // Test Firebase configuration
        async function testFirebaseConfig() {
            try {
                log('Testing Firebase configuration...');
                const testDoc = await db.collection('test').doc('connection').get();
                log('✅ Firebase connection successful');
                document.getElementById('firebase-status').innerHTML = '<span style="color: green;">✅ Connected</span>';
            } catch (error) {
                log(`❌ Firebase connection failed: ${error.message}`, 'error');
                document.getElementById('firebase-status').innerHTML = '<span style="color: red;">❌ Failed</span>';
            }
        }

        // Authentication functions
        async function signInAnonymously() {
            try {
                log('Signing in anonymously...');
                const result = await auth.signInAnonymously();
                currentUser = result.user;
                log(`✅ Signed in as: ${currentUser.uid}`);
                document.getElementById('auth-status').innerHTML = `<span style="color: green;">✅ Signed in: ${currentUser.uid}</span>`;
                document.getElementById('create-listing-btn').disabled = false;
            } catch (error) {
                log(`❌ Sign in failed: ${error.message}`, 'error');
                document.getElementById('auth-status').innerHTML = '<span style="color: red;">❌ Sign in failed</span>';
            }
        }

        async function signOut() {
            try {
                await auth.signOut();
                currentUser = null;
                log('✅ Signed out');
                document.getElementById('auth-status').innerHTML = '<span style="color: gray;">Signed out</span>';
                document.getElementById('create-listing-btn').disabled = true;
                document.getElementById('purchase-btn').disabled = true;
            } catch (error) {
                log(`❌ Sign out failed: ${error.message}`, 'error');
            }
        }

        // Create test listing
        async function createTestListing() {
            if (!currentUser) {
                log('❌ Please sign in first', 'error');
                return;
            }

            try {
                log('Creating test listing...');
                
                const listingData = {
                    title: 'Test Textbook - Advanced Mathematics',
                    description: 'Perfect condition textbook for testing the payment flow',
                    price: 25.99,
                    category: 'textbooks',
                    condition: 'excellent',
                    userId: currentUser.uid,
                    userName: 'Test Seller',
                    userEmail: currentUser.email || '<EMAIL>',
                    images: ['https://via.placeholder.com/300x400?text=Test+Book'],
                    status: 'active',
                    createdAt: firebase.firestore.FieldValue.serverTimestamp(),
                    updatedAt: firebase.firestore.FieldValue.serverTimestamp()
                };

                const docRef = await db.collection('listings').add(listingData);
                testListingId = docRef.id;
                
                log(`✅ Test listing created: ${testListingId}`);
                document.getElementById('listing-status').innerHTML = `<span style="color: green;">✅ Listing created: ${testListingId}</span>`;
                document.getElementById('purchase-btn').disabled = false;
                
            } catch (error) {
                log(`❌ Failed to create listing: ${error.message}`, 'error');
                document.getElementById('listing-status').innerHTML = '<span style="color: red;">❌ Failed to create listing</span>';
            }
        }

        // Purchase item
        async function purchaseItem() {
            if (!currentUser || !testListingId) {
                log('❌ Please sign in and create a listing first', 'error');
                return;
            }

            try {
                log('Creating checkout session...');
                
                const createCheckoutSession = functions.httpsCallable('createCheckoutSession');
                const result = await createCheckoutSession({
                    listingId: testListingId,
                    quantity: 1
                });

                log(`✅ Checkout session created: ${result.data.sessionId}`);
                latestOrderId = result.data.orderId;
                
                // Redirect to Stripe Checkout
                log('Redirecting to Stripe Checkout...');
                window.open(result.data.sessionUrl, '_blank');
                
                document.getElementById('check-order-btn').disabled = false;
                
            } catch (error) {
                log(`❌ Failed to create checkout session: ${error.message}`, 'error');
            }
        }

        // Test webhook endpoint
        async function testWebhookEndpoint() {
            try {
                log('Testing webhook endpoint...');
                
                const response = await fetch('https://us-central1-h1c1-798a8.cloudfunctions.net/stripeWebhook', {
                    method: 'GET'
                });
                
                log(`Webhook response: ${response.status} ${response.statusText}`);
                
                if (response.status === 405) {
                    log('✅ Webhook endpoint is working correctly');
                    document.getElementById('webhook-status').innerHTML = '<span style="color: green;">✅ Webhook endpoint working</span>';
                } else {
                    log('⚠️ Unexpected webhook response');
                    document.getElementById('webhook-status').innerHTML = '<span style="color: orange;">⚠️ Unexpected response</span>';
                }
                
            } catch (error) {
                log(`❌ Webhook test failed: ${error.message}`, 'error');
                document.getElementById('webhook-status').innerHTML = '<span style="color: red;">❌ Webhook test failed</span>';
            }
        }

        // Check order status
        async function checkOrderStatus() {
            if (!latestOrderId) {
                log('❌ No order to check', 'error');
                return;
            }

            try {
                log(`Checking order status: ${latestOrderId}`);
                
                const orderDoc = await db.collection('orders').doc(latestOrderId).get();
                
                if (orderDoc.exists) {
                    const orderData = orderDoc.data();
                    log(`📦 Order Status: ${orderData.status}`);
                    log(`💰 Total Amount: $${orderData.totalAmount}`);
                    
                    if (orderData.secretCode) {
                        log(`🔐 Secret Code: ${orderData.secretCode}`);
                    }
                    
                    document.getElementById('order-status').innerHTML = `
                        <div style="color: green;">
                            <strong>Order ID:</strong> ${latestOrderId}<br>
                            <strong>Status:</strong> ${orderData.status}<br>
                            <strong>Amount:</strong> $${orderData.totalAmount}<br>
                            ${orderData.secretCode ? `<strong>Secret Code:</strong> ${orderData.secretCode}<br>` : ''}
                        </div>
                    `;
                } else {
                    log('❌ Order not found');
                    document.getElementById('order-status').innerHTML = '<span style="color: red;">❌ Order not found</span>';
                }
                
            } catch (error) {
                log(`❌ Failed to check order: ${error.message}`, 'error');
            }
        }

        // Check notifications
        async function checkNotifications() {
            if (!currentUser) {
                log('❌ Please sign in first', 'error');
                return;
            }

            try {
                log('Checking notifications...');
                
                const notifications = await db.collection('notifications')
                    .where('userId', '==', currentUser.uid)
                    .orderBy('createdAt', 'desc')
                    .limit(5)
                    .get();
                
                log(`📧 Found ${notifications.size} notifications`);
                
                let notificationHtml = '<div>';
                notifications.forEach(doc => {
                    const notification = doc.data();
                    notificationHtml += `
                        <div style="margin: 5px 0; padding: 5px; border: 1px solid #ddd; border-radius: 3px;">
                            <strong>${notification.type}:</strong> ${notification.title}<br>
                            <small>${notification.message}</small>
                        </div>
                    `;
                    log(`  - ${notification.type}: ${notification.title}`);
                });
                notificationHtml += '</div>';
                
                document.getElementById('notifications-status').innerHTML = notificationHtml;
                
            } catch (error) {
                log(`❌ Failed to check notifications: ${error.message}`, 'error');
            }
        }

        // Initialize page
        window.onload = function() {
            log('🚀 Payment flow test page loaded');
            log('📋 Instructions:');
            log('1. Test Firebase connection');
            log('2. Sign in anonymously');
            log('3. Create a test listing');
            log('4. Purchase the item (opens Stripe Checkout)');
            log('5. Complete payment with test card: 4242 4242 4242 4242');
            log('6. Check order status and notifications');
            log('7. Verify webhook processing in Firebase logs');
        };
    </script>
</body>
</html>
