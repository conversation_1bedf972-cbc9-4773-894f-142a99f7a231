import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate, Link } from 'react-router-dom';
import { CheckCircle, Package, ArrowRight, MessageCircle } from 'lucide-react';
import { useAuth } from '../hooks/useAuth';

const OrderSuccess: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const [orderDetails, setOrderDetails] = useState<any>(null);

  const orderId = searchParams.get('orderId') || searchParams.get('order_id');
  const _sessionId = searchParams.get('session_id');
  const amount = searchParams.get('amount');
  const itemName = searchParams.get('item');
  const paidWithWallet = searchParams.get('paid_with_wallet') === 'true';
  const walletAmount = searchParams.get('wallet_amount');

  useEffect(() => {
    if (!currentUser) {
      navigate('/login-type');
      return;
    }

    // Set order details from URL params
    if (orderId) {
      setOrderDetails({
        id: orderId,
        amount: amount ? parseFloat(amount) : (walletAmount ? parseFloat(walletAmount) : 0),
        itemName: itemName ? decodeURIComponent(itemName) : 'Your Order',
        status: 'payment_succeeded',
        paidWithWallet: paidWithWallet,
        walletAmount: walletAmount ? parseFloat(walletAmount) : 0,
        createdAt: new Date().toISOString()
      });
    }
  }, [currentUser, navigate, orderId, amount, itemName]);

  if (!currentUser) {
    return null;
  }

  if (!orderDetails) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading order details...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Success Header */}
        <div className="text-center mb-8">
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 dark:bg-green-900/30 mb-4">
            <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            {orderDetails.paidWithWallet ? 'Order Paid with Wallet!' : 'Payment Successful!'}
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-400">
            {orderDetails.paidWithWallet
              ? `Your order was paid using $${orderDetails.walletAmount.toFixed(2)} from your wallet balance.`
              : 'Thank you for your purchase. Your order has been confirmed.'
            }
          </p>
        </div>

        {/* Order Summary Card */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Order Summary
            </h2>
          </div>
          <div className="px-6 py-4">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="font-medium text-gray-900 dark:text-white">
                  {orderDetails.itemName}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Order ID: {orderDetails.id}
                </p>
              </div>
              <div className="text-right">
                {orderDetails.paidWithWallet ? (
                  <div>
                    <p className="text-lg font-semibold text-green-600 dark:text-green-400">
                      Paid with Wallet
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      ${orderDetails.walletAmount.toFixed(2)}
                    </p>
                  </div>
                ) : (
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">
                    ${orderDetails.amount.toFixed(2)}
                  </p>
                )}
                <p className="text-sm text-green-600 dark:text-green-400">
                  Paid
                </p>
              </div>
            </div>
            
            <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
              <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                <Package className="h-4 w-4 mr-2" />
                <span>Order placed on {new Date(orderDetails.createdAt).toLocaleDateString()}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Next Steps */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              What's Next?
            </h2>
          </div>
          <div className="px-6 py-4">
            <div className="space-y-4">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="flex items-center justify-center h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900/30">
                    <span className="text-sm font-medium text-blue-600 dark:text-blue-400">1</span>
                  </div>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                    Seller Confirmation
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    The seller will be notified and will prepare your item for shipping.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="flex items-center justify-center h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900/30">
                    <span className="text-sm font-medium text-blue-600 dark:text-blue-400">2</span>
                  </div>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                    Shipping & Tracking
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    You'll receive tracking information once your item ships.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="flex items-center justify-center h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900/30">
                    <span className="text-sm font-medium text-blue-600 dark:text-blue-400">3</span>
                  </div>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                    Delivery Confirmation
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Confirm delivery with the secret code to complete the transaction.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4">
          <Link
            to="/orders"
            className="flex-1 bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center justify-center"
          >
            <Package className="h-5 w-5 mr-2" />
            View Order History
          </Link>
          
          <Link
            to="/messages"
            className="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center justify-center"
          >
            <MessageCircle className="h-5 w-5 mr-2" />
            Message Seller
          </Link>
          
          <Link
            to="/dashboard"
            className="flex-1 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 px-6 py-3 rounded-lg font-medium transition-colors flex items-center justify-center"
          >
            Continue Shopping
            <ArrowRight className="h-5 w-5 ml-2" />
          </Link>
        </div>

        {/* Support Info */}
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Need help? Contact our support team at{' '}
            <a href="mailto:<EMAIL>" className="text-primary-600 hover:text-primary-700">
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default OrderSuccess;
