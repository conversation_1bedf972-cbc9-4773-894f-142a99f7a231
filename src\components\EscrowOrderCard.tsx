import React, { useState } from 'react';
import { Clock, Package, Truck, CheckCircle, AlertCircle, Eye, RotateCcw } from 'lucide-react';
import { useStripeCheckout } from '../hooks/useStripeCheckout';
import { Order } from '../firebase/types';

interface EscrowOrderCardProps {
  order: Order;
  userRole: 'buyer' | 'seller';
  onOrderUpdate?: () => void;
}

const EscrowOrderCard: React.FC<EscrowOrderCardProps> = ({ order, userRole, onOrderUpdate }) => {
  const { releaseFundsWithCode, markDeliveryCompleted, requestReturn, isLoading } = useStripeCheckout();
  const [secretCode, setSecretCode] = useState('');
  const [showSecretInput, setShowSecretInput] = useState(false);
  const [showReturnForm, setShowReturnForm] = useState(false);
  const [returnReason, setReturnReason] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'in_progress':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'shipped':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
      case 'delivered':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'return_requested':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'in_progress':
        return 'In Progress';
      case 'shipped':
        return 'Shipped';
      case 'delivered':
        return 'Delivered';
      case 'completed':
        return 'Completed';
      case 'return_requested':
        return 'Return Requested';
      default:
        return status;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'in_progress':
        return <Clock className="w-4 h-4" />;
      case 'shipped':
        return <Truck className="w-4 h-4" />;
      case 'delivered':
        return <Package className="w-4 h-4" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4" />;
      case 'return_requested':
        return <RotateCcw className="w-4 h-4" />;
      default:
        return <AlertCircle className="w-4 h-4" />;
    }
  };

  const handleSecretCodeSubmit = async () => {
    if (!secretCode || secretCode.length !== 6) {
      setError('Please enter a valid 6-digit secret code');
      return;
    }

    try {
      setError(null);
      await releaseFundsWithCode(order.id, secretCode);
      setSuccess('Funds released successfully!');
      setShowSecretInput(false);
      setSecretCode('');
      onOrderUpdate?.();
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to release funds');
    }
  };

  const handleMarkDelivered = async () => {
    try {
      setError(null);
      await markDeliveryCompleted(order.id);
      setSuccess('Order marked as delivered!');
      onOrderUpdate?.();
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to mark as delivered');
    }
  };

  const handleRequestReturn = async () => {
    try {
      setError(null);
      await requestReturn(order.id, returnReason);
      setSuccess('Return request submitted!');
      setShowReturnForm(false);
      setReturnReason('');
      onOrderUpdate?.();
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to request return');
    }
  };

  const isReturnEligible = () => {
    if (!order.returnEligibleUntil || order.status !== 'delivered') return false;
    const deadline = order.returnEligibleUntil.toDate();
    return new Date() < deadline;
  };

  const getTimeRemaining = () => {
    if (!order.returnEligibleUntil) return null;
    const deadline = order.returnEligibleUntil.toDate();
    const now = new Date();
    const diff = deadline.getTime() - now.getTime();
    
    if (diff <= 0) return 'Expired';
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    if (hours < 24) {
      return `${hours}h remaining`;
    }
    const days = Math.floor(hours / 24);
    return `${days}d remaining`;
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6 border border-gray-200 dark:border-gray-700">
      {/* Order Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-start space-x-4">
          <img
            src={order.listingImage || '/placeholder-image.jpg'}
            alt={order.listingTitle}
            className="w-16 h-16 rounded-xl object-cover"
          />
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
              {order.listingTitle}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Order #{order.id.slice(-8)}
            </p>
            <p className="text-lg font-bold text-gray-900 dark:text-white">
              ${order.finalStripeAmount?.toFixed(2) || order.amount?.toFixed(2)}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {getStatusIcon(order.status)}
          <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(order.status)}`}>
            {getStatusText(order.status)}
          </span>
        </div>
      </div>

      {/* Delivery Type */}
      <div className="mb-4">
        <span className="text-sm text-gray-600 dark:text-gray-400">
          Delivery: {order.deliveryType === 'shipping' ? 'Shipping' : 'In-Person'}
        </span>
        {order.shippingTrackingNumber && (
          <div className="mt-1">
            <span className="text-sm text-blue-600 dark:text-blue-400">
              Tracking: {order.shippingTrackingNumber}
            </span>
          </div>
        )}
      </div>

      {/* Return Window Info */}
      {order.status === 'delivered' && (
        <div className="mb-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
          <div className="flex items-center justify-between">
            <span className="text-sm text-yellow-800 dark:text-yellow-400">
              Return window: {getTimeRemaining()}
            </span>
            {userRole === 'buyer' && isReturnEligible() && (
              <button
                onClick={() => setShowReturnForm(true)}
                className="text-sm text-yellow-600 hover:text-yellow-700 dark:text-yellow-400 dark:hover:text-yellow-300"
              >
                Request Return
              </button>
            )}
          </div>
        </div>
      )}

      {/* Error/Success Messages */}
      {error && (
        <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
          <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
        </div>
      )}
      
      {success && (
        <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <p className="text-sm text-green-600 dark:text-green-400">{success}</p>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-2">
        {/* Buyer Actions */}
        {userRole === 'buyer' && order.status === 'delivered' && !order.releasedToSeller && (
          <button
            onClick={() => setShowSecretInput(true)}
            disabled={isLoading}
            className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
          >
            <CheckCircle className="w-4 h-4" />
            <span>Enter Secret Code</span>
          </button>
        )}

        {/* Seller Actions */}
        {userRole === 'seller' && order.deliveryType === 'in_person' && order.status === 'in_progress' && (
          <button
            onClick={handleMarkDelivered}
            disabled={isLoading}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            <Package className="w-4 h-4" />
            <span>Mark as Delivered</span>
          </button>
        )}

        {/* View Details */}
        <button className="flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700">
          <Eye className="w-4 h-4" />
          <span>View Details</span>
        </button>
      </div>

      {/* Secret Code Input Modal */}
      {showSecretInput && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">Enter Secret Code</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              Enter the 6-digit secret code provided by the seller to confirm delivery and release funds.
            </p>
            <input
              type="text"
              value={secretCode}
              onChange={(e) => setSecretCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
              placeholder="000000"
              className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg mb-4 text-center text-2xl tracking-widest"
              maxLength={6}
            />
            <div className="flex space-x-3">
              <button
                onClick={() => setShowSecretInput(false)}
                className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                Cancel
              </button>
              <button
                onClick={handleSecretCodeSubmit}
                disabled={isLoading || secretCode.length !== 6}
                className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
              >
                Confirm
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Return Request Modal */}
      {showReturnForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">Request Return</h3>
            <textarea
              value={returnReason}
              onChange={(e) => setReturnReason(e.target.value)}
              placeholder="Please explain why you want to return this item..."
              className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg mb-4 h-24 resize-none"
            />
            <div className="flex space-x-3">
              <button
                onClick={() => setShowReturnForm(false)}
                className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                Cancel
              </button>
              <button
                onClick={handleRequestReturn}
                disabled={isLoading}
                className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50"
              >
                Request Return
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EscrowOrderCard;
