import React, { useState, useEffect } from 'react';
import { Shield, Lock, Eye, EyeOff, Settings } from 'lucide-react';
import { httpsCallable } from 'firebase/functions';
import { functions } from '../../firebase/config';
import { useAuth } from '../../hooks/useAuth';

interface AdminPinAuthProps {
  onPinVerified: () => void;
  onSetupPin?: () => void;
}

const AdminPinAuth: React.FC<AdminPinAuthProps> = ({ onPinVerified, onSetupPin: _onSetupPin }) => {
  const { currentUser: _currentUser } = useAuth();
  const [pin, setPin] = useState('');
  const [showPin, setShowPin] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSetupMode, setIsSetupMode] = useState(false);
  const [confirmPin, setConfirmPin] = useState('');
  const [needsSetup, setNeedsSetup] = useState(false);

  useEffect(() => {
    // Check if PIN is already set up
    checkPinSetup();
  }, []);

  const checkPinSetup = async () => {
    try {
      const verifyPin = httpsCallable(functions, 'verifyAdminPin');
      await verifyPin({ pin: '00000000' }); // Test with dummy PIN
    } catch (error: any) {
      console.log('PIN setup check error:', error);
      if (error.code === 'functions/not-found') {
        console.log('PIN not set up, enabling setup mode');
        setNeedsSetup(true);
        setIsSetupMode(true);
      } else if (error.code === 'functions/permission-denied') {
        console.log('PIN is set up (dummy PIN rejected)');
        setNeedsSetup(false);
        setIsSetupMode(false);
      }
    }
  };

  const handlePinSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (pin.length !== 8) {
      setError('PIN must be exactly 8 digits');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      if (isSetupMode) {
        if (pin !== confirmPin) {
          setError('PINs do not match');
          setIsLoading(false);
          return;
        }

        const setAdminPin = httpsCallable(functions, 'setAdminPin');
        await setAdminPin({ pin });
        
        setIsSetupMode(false);
        setNeedsSetup(false);
        setPin('');
        setConfirmPin('');
        alert('Admin PIN set successfully! Please enter your PIN to continue.');
      } else {
        const verifyPin = httpsCallable(functions, 'verifyAdminPin');
        await verifyPin({ pin });
        
        // Store PIN verification in session
        sessionStorage.setItem('adminPinVerified', 'true');
        sessionStorage.setItem('adminPinTime', Date.now().toString());
        
        onPinVerified();
      }
    } catch (error: any) {
      console.error('PIN error:', error);

      if (error.code === 'functions/not-found') {
        setError('Admin PIN not set up. Please set up your PIN first.');
        setNeedsSetup(true);
        setIsSetupMode(true);
      } else if (error.code === 'functions/permission-denied') {
        setError('❌ Wrong PIN - Unauthorized entry. Please try again.');
      } else if (error.code === 'functions/unauthenticated') {
        setError('Please log in as an admin first.');
      } else if (error.code === 'functions/internal') {
        // Check if it's specifically a PIN validation error
        if (error.message?.includes('Invalid PIN') || error.message?.includes('permission-denied')) {
          setError('❌ Wrong PIN - Unauthorized entry. Please try again.');
        } else {
          setError('Server error. Please try again in a moment.');
        }
      } else if (error.message?.includes('CORS') || error.message?.includes('Failed to fetch')) {
        setError('Connection error. Please refresh the page and try again.');
      } else if (error.message?.includes('Invalid PIN') || error.message?.includes('permission-denied')) {
        setError('❌ Wrong PIN - Unauthorized entry. Please try again.');
      } else {
        setError(error.message || 'An unexpected error occurred. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handlePinChange = (value: string) => {
    // Only allow digits and limit to 8 characters
    const numericValue = value.replace(/\D/g, '').slice(0, 8);
    setPin(numericValue);
  };

  const handleConfirmPinChange = (value: string) => {
    // Only allow digits and limit to 8 characters
    const numericValue = value.replace(/\D/g, '').slice(0, 8);
    setConfirmPin(numericValue);
  };

  // Development helper to clear session storage
  const clearSession = () => {
    sessionStorage.removeItem('adminPinVerified');
    sessionStorage.removeItem('adminPinTime');
    console.log('Session storage cleared');
    alert('Session cleared! Refresh the page to see PIN prompt again.');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="mx-auto w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mb-4">
              {isSetupMode ? (
                <Settings className="w-8 h-8 text-blue-600 dark:text-blue-400" />
              ) : (
                <Shield className="w-8 h-8 text-blue-600 dark:text-blue-400" />
              )}
            </div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              {isSetupMode ? 'Set Up Admin PIN' : 'Admin Security Verification'}
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              {isSetupMode 
                ? 'Create an 8-digit PIN for secure admin access'
                : 'Enter your 8-digit PIN to access the admin dashboard'
              }
            </p>
          </div>

          {/* PIN Form */}
          <form onSubmit={handlePinSubmit} className="space-y-6">
            {/* PIN Input */}
            <div className="space-y-2">
              <label htmlFor="admin-pin" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {isSetupMode ? 'Create PIN' : 'Enter PIN'}
              </label>
              <div className="relative">
                <Lock className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                <input
                  id="admin-pin"
                  name="pin"
                  type={showPin ? 'text' : 'password'}
                  value={pin}
                  onChange={(e) => handlePinChange(e.target.value)}
                  className="w-full pl-10 pr-12 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all text-center text-2xl tracking-widest"
                  placeholder="••••••••"
                  maxLength={8}
                  required
                  disabled={isLoading}
                />
                <button
                  type="button"
                  onClick={() => setShowPin(!showPin)}
                  className="absolute right-3 top-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                  disabled={isLoading}
                >
                  {showPin ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
            </div>

            {/* Confirm PIN Input (Setup Mode Only) */}
            {isSetupMode && (
              <div className="space-y-2">
                <label htmlFor="admin-confirm-pin" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Confirm PIN
                </label>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                  <input
                    id="admin-confirm-pin"
                    name="confirmPin"
                    type={showPin ? 'text' : 'password'}
                    value={confirmPin}
                    onChange={(e) => handleConfirmPinChange(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all text-center text-2xl tracking-widest"
                    placeholder="••••••••"
                    maxLength={8}
                    required
                    disabled={isLoading}
                  />
                </div>
              </div>
            )}

            {/* Error Message */}
            {error && (
              <div className="bg-red-50 dark:bg-red-900/20 border-2 border-red-300 dark:border-red-700 rounded-lg p-4 animate-pulse">
                <div className="flex items-center justify-center space-x-2">
                  <div className="text-red-500 text-lg">🚫</div>
                  <p className="text-sm font-semibold text-red-700 dark:text-red-300 text-center">{error}</p>
                </div>
              </div>
            )}

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading || pin.length !== 8 || (isSetupMode && confirmPin.length !== 8)}
              className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-3 px-4 rounded-xl transition-colors disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <div className="flex items-center justify-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>{isSetupMode ? 'Setting up...' : 'Verifying...'}</span>
                </div>
              ) : (
                isSetupMode ? 'Set PIN' : 'Verify PIN'
              )}
            </button>
          </form>

          {/* Setup Instructions */}
          {isSetupMode && (
            <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <h3 className="text-sm font-medium text-blue-800 dark:text-blue-300 mb-2">
                PIN Security Guidelines:
              </h3>
              <ul className="text-xs text-blue-700 dark:text-blue-400 space-y-1">
                <li>• Use a unique 8-digit combination</li>
                <li>• Avoid sequential numbers (12345678)</li>
                <li>• Don't use repeated digits (11111111)</li>
                <li>• Keep your PIN secure and don't share it</li>
              </ul>
            </div>
          )}

          {/* Toggle Setup Mode */}
          {!needsSetup && !isSetupMode && (
            <div className="mt-6 text-center">
              <button
                type="button"
                onClick={() => setIsSetupMode(true)}
                className="text-sm text-blue-600 dark:text-blue-400 hover:underline"
              >
                Need to change your PIN?
              </button>
            </div>
          )}

          {/* Development Helper */}
          {process.env.NODE_ENV === 'development' && (
            <div className="mt-4 text-center">
              <button
                type="button"
                onClick={clearSession}
                className="text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
              >
                [DEV] Clear Session
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdminPinAuth;
